package com.bm.atool.scheduled;

/**
 * 定时脚本系统常量定义
 */
public class ScheduledScriptConstants {
    
    // ========== 执行类型常量 ==========
    public static final String EXECUTION_TYPE_IMMEDIATE = "immediate";
    public static final String EXECUTION_TYPE_SCHEDULED = "scheduled";
    public static final String EXECUTION_TYPE_REPEAT = "repeat";
    
    // ========== Socket消息类型 ==========
    public static final String SOCKET_EVENT_SCHEDULED_SCRIPT = "scheduledScript";
    public static final String SOCKET_EVENT_SCRIPT_RESULT = "scriptResult";
    public static final String SOCKET_EVENT_TASK_STATUS = "taskStatus";
    public static final String SOCKET_EVENT_SCRIPT_DOWNLOAD = "scriptDownload";
    
    // ========== 文件和目录常量 ==========
    public static final String SCRIPTS_DIR = "scripts";
    public static final String TASKS_DIR = "tasks";
    public static final String LOGS_DIR = "logs";
    public static final String TEMP_DIR = "temp";
    
    public static final String SCRIPT_FILE_EXTENSION = ".js";
    public static final String METADATA_FILE_EXTENSION = ".meta";
    public static final String TASK_FILE_EXTENSION = ".json";
    public static final String LOG_FILE_EXTENSION = ".log";
    
    public static final String ACTIVE_TASKS_FILE = "active_tasks.json";
    public static final String PERSISTENT_TASKS_FILE = "persistent_tasks.json";
    
    // ========== 默认配置常量 ==========
    public static final long DEFAULT_TIMEOUT_MS = 30000; // 30秒
    public static final int DEFAULT_MAX_EXECUTIONS = 1;
    public static final int DEFAULT_PRIORITY = 5;
    public static final int DEFAULT_MAX_CONCURRENT = 5;
    
    // ========== 重试和清理常量 ==========
    public static final int MAX_DOWNLOAD_RETRIES = 3;
    public static final long DOWNLOAD_RETRY_DELAY_MS = 1000;
    public static final long CLEANUP_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24小时
    public static final long DEFAULT_HISTORY_RETENTION_MS = 7 * 24 * 60 * 60 * 1000; // 7天
    
    // ========== 错误代码常量 ==========
    public static final String ERROR_SCRIPT_NOT_FOUND = "SCRIPT_NOT_FOUND";
    public static final String ERROR_TASK_NOT_FOUND = "TASK_NOT_FOUND";
    public static final String ERROR_INVALID_CONFIG = "INVALID_CONFIG";
    public static final String ERROR_EXECUTION_TIMEOUT = "EXECUTION_TIMEOUT";
    public static final String ERROR_EXECUTION_FAILED = "EXECUTION_FAILED";
    public static final String ERROR_STORAGE_FAILED = "STORAGE_FAILED";
    public static final String ERROR_DOWNLOAD_FAILED = "DOWNLOAD_FAILED";
    public static final String ERROR_PERMISSION_DENIED = "PERMISSION_DENIED";
    public static final String ERROR_RESOURCE_EXHAUSTED = "RESOURCE_EXHAUSTED";
    
    // ========== 日志标签常量 ==========
    public static final String TAG_SCRIPT_MANAGER = "ScheduledScriptManager";
    public static final String TAG_TASK_SCHEDULER = "TaskScheduler";
    public static final String TAG_SCRIPT_STORAGE = "ScriptStorage";
    public static final String TAG_SCRIPT_DOWNLOADER = "ScriptDownloader";
    
    // ========== 广播Action常量 ==========
    public static final String ACTION_TASK_STARTED = "com.bm.atool.TASK_STARTED";
    public static final String ACTION_TASK_COMPLETED = "com.bm.atool.TASK_COMPLETED";
    public static final String ACTION_TASK_FAILED = "com.bm.atool.TASK_FAILED";
    public static final String ACTION_SCRIPT_DOWNLOADED = "com.bm.atool.SCRIPT_DOWNLOADED";
    
    // ========== Intent Extra常量 ==========
    public static final String EXTRA_TASK_ID = "task_id";
    public static final String EXTRA_SCRIPT_ID = "script_id";
    public static final String EXTRA_EXECUTION_ID = "execution_id";
    public static final String EXTRA_RESULT = "result";
    public static final String EXTRA_ERROR = "error";
    public static final String EXTRA_SUCCESS = "success";
    
    // ========== 权限和安全常量 ==========
    public static final int MAX_SCRIPT_SIZE_BYTES = 1024 * 1024; // 1MB
    public static final int MAX_CONCURRENT_DOWNLOADS = 3;
    public static final String[] ALLOWED_SCRIPT_EXTENSIONS = {".js", ".javascript"};
    
    // ========== 性能监控常量 ==========
    public static final long PERFORMANCE_LOG_THRESHOLD_MS = 5000; // 5秒
    public static final int MAX_EXECUTION_HISTORY_PER_TASK = 100;
    public static final long MAX_STORAGE_SIZE_BYTES = 100 * 1024 * 1024; // 100MB
}