package com.bm.atool.utils;

import android.content.Context;
import android.util.Log;

import com.bm.atool.model.ExecutionConfig;
import com.bm.atool.model.ScheduledScriptRequest;
import com.bm.atool.model.ScriptTask;
import com.bm.atool.model.TaskStatus;
import com.bm.atool.scheduled.ScheduledScriptConstants;
import com.bm.atool.scheduled.ScheduledScriptManager;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 定时脚本工具类
 * 提供便捷的脚本管理方法
 */
public class ScheduledScriptUtils {
    private static final String TAG = "ScheduledScriptUtils";
    
    /**
     * 创建立即执行的脚本
     * @param context 上下文
     * @param scriptId 脚本ID
     * @param scriptContent 脚本内容
     * @param params 脚本参数
     * @return 脚本ID，失败返回null
     */
    public static String createImmediateScript(Context context, String scriptId, 
                                             String scriptContent, Map<String, Object> params) {
        return createScript(context, scriptId, scriptContent, params, 
                          ScheduledScriptConstants.EXECUTION_TYPE_IMMEDIATE, 0, 0, 1);
    }
    
    /**
     * 创建延时执行的脚本
     * @param context 上下文
     * @param scriptId 脚本ID
     * @param scriptContent 脚本内容
     * @param params 脚本参数
     * @param delayMs 延时毫秒数
     * @return 脚本ID，失败返回null
     */
    public static String createDelayedScript(Context context, String scriptId, 
                                           String scriptContent, Map<String, Object> params, long delayMs) {
        long scheduleTime = System.currentTimeMillis() + delayMs;
        return createScript(context, scriptId, scriptContent, params, 
                          ScheduledScriptConstants.EXECUTION_TYPE_SCHEDULED, scheduleTime, 0, 1);
    }
    
    /**
     * 创建重复执行的脚本
     * @param context 上下文
     * @param scriptId 脚本ID
     * @param scriptContent 脚本内容
     * @param params 脚本参数
     * @param intervalMs 重复间隔毫秒数
     * @param maxExecutions 最大执行次数，-1表示无限制
     * @return 脚本ID，失败返回null
     */
    public static String createRepeatScript(Context context, String scriptId, 
                                          String scriptContent, Map<String, Object> params, 
                                          long intervalMs, int maxExecutions) {
        return createScript(context, scriptId, scriptContent, params, 
                          ScheduledScriptConstants.EXECUTION_TYPE_REPEAT, 0, intervalMs, maxExecutions);
    }
    
    /**
     * 创建脚本的通用方法
     */
    private static String createScript(Context context, String scriptId, String scriptContent, 
                                     Map<String, Object> params, String executionType, 
                                     long scheduleTime, long intervalMs, int maxExecutions) {
        try {
            ScheduledScriptManager manager = ScheduledScriptManager.getInstance(context);
            if (!manager.isInitialized()) {
                manager.initialize();
            }
            
            ScheduledScriptRequest request = new ScheduledScriptRequest();
            request.scriptId = scriptId;
            request.scriptName = "脚本 - " + scriptId;
            request.scriptContent = scriptContent;
            request.params = params;
            
            ExecutionConfig config = new ExecutionConfig();
            config.executionType = executionType;
            config.scheduleTime = scheduleTime;
            config.repeatInterval = intervalMs;
            config.maxExecutions = maxExecutions;
            config.timeout = 30000; // 30秒超时
            
            request.config = config;
            
            return manager.addScript(request);
        } catch (Exception e) {
            Log.e(TAG, "创建脚本失败: " + scriptId, e);
            return null;
        }
    }
    
    /**
     * 获取脚本执行状态信息
     * @param context 上下文
     * @param scriptId 脚本ID
     * @return 状态信息字符串
     */
    public static String getScriptStatus(Context context, String scriptId) {
        try {
            ScheduledScriptManager manager = ScheduledScriptManager.getInstance(context);
            List<ScriptTask> tasks = manager.getTasksByScript(scriptId);
            
            if (tasks.isEmpty()) {
                return "脚本不存在或没有任务";
            }
            
            StringBuilder status = new StringBuilder();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            
            for (ScriptTask task : tasks) {
                status.append("任务ID: ").append(task.taskId).append("\n");
                status.append("状态: ").append(getStatusDescription(task.status)).append("\n");
                status.append("执行次数: ").append(task.executionCount).append("\n");
                
                if (task.lastExecutionTime > 0) {
                    status.append("最后执行时间: ").append(dateFormat.format(new Date(task.lastExecutionTime))).append("\n");
                }
                
                if (task.nextExecutionTime > 0) {
                    status.append("下次执行时间: ").append(dateFormat.format(new Date(task.nextExecutionTime))).append("\n");
                }
                
                if (task.lastResult != null) {
                    status.append("最后结果: ").append(task.lastResult).append("\n");
                }
                
                if (task.lastError != null) {
                    status.append("最后错误: ").append(task.lastError).append("\n");
                }
                
                status.append("---\n");
            }
            
            return status.toString();
        } catch (Exception e) {
            Log.e(TAG, "获取脚本状态失败: " + scriptId, e);
            return "获取状态失败: " + e.getMessage();
        }
    }
    
    /**
     * 停止脚本的所有任务
     * @param context 上下文
     * @param scriptId 脚本ID
     * @return 是否成功
     */
    public static boolean stopScript(Context context, String scriptId) {
        try {
            ScheduledScriptManager manager = ScheduledScriptManager.getInstance(context);
            List<ScriptTask> tasks = manager.getTasksByScript(scriptId);
            
            boolean allStopped = true;
            for (ScriptTask task : tasks) {
                if (!manager.cancelTask(task.taskId)) {
                    allStopped = false;
                }
            }
            
            return allStopped;
        } catch (Exception e) {
            Log.e(TAG, "停止脚本失败: " + scriptId, e);
            return false;
        }
    }
    
    /**
     * 删除脚本
     * @param context 上下文
     * @param scriptId 脚本ID
     * @return 是否成功
     */
    public static boolean removeScript(Context context, String scriptId) {
        try {
            ScheduledScriptManager manager = ScheduledScriptManager.getInstance(context);
            return manager.removeScript(scriptId);
        } catch (Exception e) {
            Log.e(TAG, "删除脚本失败: " + scriptId, e);
            return false;
        }
    }
    
    /**
     * 获取所有脚本的概览信息
     * @param context 上下文
     * @return 概览信息字符串
     */
    public static String getAllScriptsOverview(Context context) {
        try {
            ScheduledScriptManager manager = ScheduledScriptManager.getInstance(context);
            List<ScriptTask> allTasks = manager.getAllTasks();
            
            if (allTasks.isEmpty()) {
                return "没有定时脚本任务";
            }
            
            int pendingCount = 0;
            int runningCount = 0;
            int completedCount = 0;
            int failedCount = 0;
            int cancelledCount = 0;
            
            for (ScriptTask task : allTasks) {
                switch (task.status) {
                    case PENDING:
                        pendingCount++;
                        break;
                    case RUNNING:
                        runningCount++;
                        break;
                    case COMPLETED:
                        completedCount++;
                        break;
                    case FAILED:
                        failedCount++;
                        break;
                    case CANCELLED:
                        cancelledCount++;
                        break;
                }
            }
            
            StringBuilder overview = new StringBuilder();
            overview.append("定时脚本任务概览:\n");
            overview.append("总任务数: ").append(allTasks.size()).append("\n");
            overview.append("等待执行: ").append(pendingCount).append("\n");
            overview.append("正在执行: ").append(runningCount).append("\n");
            overview.append("已完成: ").append(completedCount).append("\n");
            overview.append("执行失败: ").append(failedCount).append("\n");
            overview.append("已取消: ").append(cancelledCount).append("\n");
            
            return overview.toString();
        } catch (Exception e) {
            Log.e(TAG, "获取脚本概览失败", e);
            return "获取概览失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取任务状态的中文描述
     */
    private static String getStatusDescription(TaskStatus status) {
        switch (status) {
            case PENDING:
                return "等待执行";
            case RUNNING:
                return "正在执行";
            case COMPLETED:
                return "已完成";
            case PAUSED:
                return "已暂停";
            case FAILED:
                return "执行失败";
            case CANCELLED:
                return "已取消";
            default:
                return "未知状态";
        }
    }
    
    /**
     * 验证脚本内容是否有效
     * @param scriptContent 脚本内容
     * @return 是否有效
     */
    public static boolean isValidScriptContent(String scriptContent) {
        if (scriptContent == null || scriptContent.trim().isEmpty()) {
            return false;
        }
        
        // 检查脚本长度
        if (scriptContent.length() > ScheduledScriptConstants.MAX_SCRIPT_SIZE_BYTES) {
            return false;
        }
        
        // 简单的语法检查
        String trimmed = scriptContent.trim();
        return !trimmed.startsWith("<") && !trimmed.startsWith("<?"); // 不是HTML或XML
    }
    
    /**
     * 格式化时间间隔为可读字符串
     * @param intervalMs 间隔毫秒数
     * @return 格式化的字符串
     */
    public static String formatInterval(long intervalMs) {
        if (intervalMs < 1000) {
            return intervalMs + "毫秒";
        } else if (intervalMs < 60000) {
            return (intervalMs / 1000) + "秒";
        } else if (intervalMs < 3600000) {
            return (intervalMs / 60000) + "分钟";
        } else if (intervalMs < 86400000) {
            return (intervalMs / 3600000) + "小时";
        } else {
            return (intervalMs / 86400000) + "天";
        }
    }
}