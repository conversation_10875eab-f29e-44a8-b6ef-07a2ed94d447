package com.bm.atool.model;

import java.util.Map;

/**
 * 定时脚本请求模型
 * 用于接收服务器下发的脚本执行请求
 */
public class ScheduledScriptRequest {
    /**
     * 脚本唯一标识
     */
    public String scriptId;
    
    /**
     * 脚本名称
     */
    public String scriptName;
    
    /**
     * 脚本内容
     */
    public String scriptContent;
    
    /**
     * 脚本下载URL（可选，如果提供则从URL下载脚本内容）
     */
    public String downloadUrl;
    
    /**
     * 执行配置
     */
    public ExecutionConfig config;
    
    /**
     * 脚本参数，可在脚本中通过engines.myEngine().execArgv访问
     */
    public Map<String, Object> params;
    
    /**
     * 脚本版本号，用于版本控制
     */
    public String version;
    
    /**
     * 脚本描述
     */
    public String description;
}