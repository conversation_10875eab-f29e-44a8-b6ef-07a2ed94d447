package com.bm.atool.ui;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.bm.atool.R;
import com.bm.atool.Sys;
import com.bm.atool.events.EventSource;
import com.bm.atool.events.SMSEvent;
import com.bm.atool.model.SubscriptionInfoModel;
import com.bm.atool.service.SocketService;
import com.bm.atool.ui.adapters.PhoneAdapter;
import com.bm.atool.ui.adapters.SmsAdapter;
import com.bm.atool.utils.ScriptBroadcastUtil;
import com.bm.atool.utils.ScriptExecutionUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class MainFragment extends BaseFragment implements SocketService.ConnectionStatusListener {
    TextView txtUserName;
    RecyclerView phoneListView;
    RecyclerView smsListView;
    ImageView imgStatus;
    Date lastPong =  new Date(System.currentTimeMillis() - 1000 * 20);

    private static final String TAG = "MainFragment";
    private Boolean pendingInitialConnectionState = null;
    private boolean mIsSocketConnected = false;
    private boolean viewCreated = false;

    private Handler handler = new Handler(Looper.getMainLooper());
    private Runnable runnable=new Runnable() {
        @Override
        public void run() {
            Log.d(TAG, "Runnable: run() called. mIsSocketConnected: " + mIsSocketConnected);
            Context context = getContext();
            if (context == null) {
                Log.w(TAG, "Runnable: Context is null, cannot update UI.");
                handler.postDelayed(this, 3000);
                return;
            }

            if (!Sys.isLogin()) {
                Log.d(TAG, "Runnable: User not logged in, hiding status icon.");
                if (imgStatus != null) imgStatus.setVisibility(View.INVISIBLE);
            } else {
                if (imgStatus == null) {
                    Log.w(TAG, "Runnable: imgStatus is null, cannot update UI.");
                    handler.postDelayed(this, 3000);
                    return;
                }
                if (mIsSocketConnected) {
                    if (Objects.nonNull(lastPong)) {
                        long diff = TimeUnit.MILLISECONDS.toSeconds(new Date().getTime() - lastPong.getTime());
                        Log.d(TAG, "Runnable: mIsSocketConnected=true. Time since last pong: " + diff + "s");
                        if (diff > 35) {
                            Log.d(TAG, "Runnable: Pong timeout. Setting error icon.");
                            imgStatus.setVisibility(View.VISIBLE);
                            imgStatus.setImageResource(android.R.drawable.stat_notify_error);
                        } else {
                            Log.d(TAG, "Runnable: Pong OK. Setting/keeping online icon.");
                            imgStatus.setVisibility(View.VISIBLE);
                            imgStatus.setImageResource(android.R.drawable.presence_online);
                        }
                    } else {
                        Log.d(TAG, "Runnable: mIsSocketConnected=true, but lastPong is null. Assuming online from initial connect.");
                        imgStatus.setVisibility(View.VISIBLE);
                        imgStatus.setImageResource(android.R.drawable.presence_online);
                    }
                } else {
                    Log.d(TAG, "Runnable: mIsSocketConnected=false. Setting offline icon.");
                    imgStatus.setVisibility(View.VISIBLE);
                    imgStatus.setImageResource(android.R.drawable.presence_offline);
                }
            }
            handler.postDelayed(this, 3000);
        }
    };
    BroadcastReceiver onlineStatusReceiver=new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            Log.d(TAG, "onlineStatusReceiver: onReceive() called");
            lastPong = new Date();
            
            // 检查是否是立即连接的标志
            boolean immediateConnect = intent.getBooleanExtra("immediate_connect", false);
            
            if (!mIsSocketConnected || immediateConnect) {
                Log.d(TAG, "onlineStatusReceiver: Socket was not marked as connected or immediate connect requested. Updating state and UI.");
                mIsSocketConnected = true;
                // 立即更新UI，不等待runnable的下一个周期
                updateConnectionStatus(true);
            }
        }
    };
    
    EventSource.IEventListener<ArrayList<SubscriptionInfoModel>> phoneChangedEventListener = new EventSource.IEventListener<ArrayList<SubscriptionInfoModel>>() {
        @Override
        public void onEvent(ArrayList<SubscriptionInfoModel> event) {
            Log.d(TAG, "phoneChangedEventListener: onEvent() called");
            if(Objects.nonNull(phoneListView)){
                phoneListView.setAdapter(new PhoneAdapter(Sys.phones));
            }
        }
    };

    EventSource.IEventListener<SMSEvent> smsChangedEventListener = new EventSource.IEventListener<SMSEvent>() {
        @Override
        public void onEvent(SMSEvent event) {
            Log.d(TAG, "smsChangedEventListener: onEvent() called");
            if(Objects.nonNull(smsListView)){
                smsListView.setAdapter(new SmsAdapter(Sys.lastSMSList));
            }
        }
    };

    EventSource.IEventListener<Sys.LoginEvent> loginChangeEventListener = new EventSource.IEventListener<Sys.LoginEvent>() {
        @Override
        public void onEvent(Sys.LoginEvent event) {
            Log.d(TAG, "loginChangeEventListener: onEvent() called with username: " + event.username);
            if(Objects.nonNull(txtUserName)){
                txtUserName.setText(event.username);
            }

            if (getContext() != null && Sys.isLogin()) {
                Log.d(TAG, "登录事件触发，尝试预初始化Socket连接");
                SocketService.preInitSocketConnection(getContext());
            }
        }
    };
    public MainFragment(){
        super();
        this.setTitle(" HOME ");
        Log.d(TAG, "MainFragment constructor");
        Sys.phoneEventSource.addEventListener(phoneChangedEventListener);
        Sys.smsEventSource.addEventListener(smsChangedEventListener);
        Sys.loginEventSource.addEventListener(loginChangeEventListener);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView() called");
        View view =inflater.inflate(R.layout.fragment_main, container, false);

        txtUserName=view.findViewById(R.id.txtUserName);
        txtUserName.setText(Sys.getUserName());

        phoneListView= (RecyclerView)view.findViewById(R.id.phoneListView);
        phoneListView.setLayoutManager(new LinearLayoutManager(this.getContext())); ;
        phoneListView.setAdapter(new PhoneAdapter(Sys.phones));

        smsListView = (RecyclerView)view.findViewById(R.id.smsListView);
        smsListView.setLayoutManager(new LinearLayoutManager(this.getContext()));
        smsListView.setAdapter(new SmsAdapter(Sys.lastSMSList));

        imgStatus = view.findViewById(R.id.imgStatus);
        Log.d(TAG, "onCreateView: imgStatus initialized.");
        imgStatus.setVisibility(View.INVISIBLE);
        
        // 设置测试按钮的点击事件
        Button btnTestScript = view.findViewById(R.id.btnTestScript);
        btnTestScript.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "测试按钮被点击，发送模拟脚本广播");
                if (getContext() != null) {
                    // 发送模拟脚本广播
                    ScriptBroadcastUtil.sendSimulateScriptBroadcast(getContext());
                    Toast.makeText(getContext(), "已发送模拟脚本广播", Toast.LENGTH_SHORT).show();
                } else {
                    Log.e(TAG, "无法获取Context，无法发送广播");
                }
            }
        });
        
        // 设置直接执行脚本按钮的点击事件
        Button btnDirectScript = view.findViewById(R.id.btnDirectScript);
        btnDirectScript.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "直接执行脚本按钮被点击");
                if (getContext() != null) {
                    Log.d(TAG, "准备调用ScriptExecutionUtil.executeSimulatedScript");
                    // 直接调用脚本执行工具执行脚本
                    boolean result = ScriptExecutionUtil.executeSimulatedScript(getContext());
                    Log.d(TAG, "ScriptExecutionUtil.executeSimulatedScript调用完成，返回值: " + result);
                } else {
                    Log.e(TAG, "无法获取Context，无法执行脚本");
                }
            }
        });
        
        viewCreated = true;

        // 如果已登录，尝试预初始化Socket连接
        if (Sys.isLogin()) {
            SocketService.preInitSocketConnection(getContext());
        }

        if (pendingInitialConnectionState != null) {
            Log.d(TAG, "onCreateView: Applying pendingInitialConnectionState: " + pendingInitialConnectionState);
            onConnectionStatusChanged(pendingInitialConnectionState);
            pendingInitialConnectionState = null;
        } else {
            Log.d(TAG, "onCreateView: No pending state. Updating view based on current mIsSocketConnected: " + mIsSocketConnected);
            updateConnectionStatus(mIsSocketConnected);
        }

        if (getContext() != null) {
            Sys.registerReceiver(getContext(),onlineStatusReceiver,new IntentFilter(Sys.ACTION_SOCKET_PONG));
            Log.d(TAG, "onCreateView: onlineStatusReceiver registered.");
        }

        checkSocketConnection();

        return view;
    }
    
    /**
     * 检查Socket的连接状态并更新UI
     */
    private void checkSocketConnection() {
        if (SocketService.isServiceRunningAndConnected()) {
            Log.d(TAG, "checkSocketConnection: Socket已连接，更新UI状态");
            mIsSocketConnected = true;
            lastPong = new Date();
            updateConnectionStatus(true);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume() called. Starting handler for runnable.");
        handler.postDelayed(runnable, 500);

        SocketService.registerConnectionStatusListener(this);

        checkSocketConnection();
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause() called. Removing handler callbacks for runnable.");
        handler.removeCallbacks(runnable);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView() called. onlineStatusReceiver being unregistered.");
        if (getContext() != null) {
            try {
                getContext().unregisterReceiver(onlineStatusReceiver);
                Log.d(TAG, "onDestroyView: onlineStatusReceiver successfully unregistered.");
            } catch (IllegalArgumentException e) {
                Log.w(TAG, "onDestroyView: onlineStatusReceiver was not registered or already unregistered.", e);
            }
        }
        viewCreated = false;
        imgStatus = null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "onDestroy() called. Removing event listeners.");
        Sys.phoneEventSource.removeEventListener(this.phoneChangedEventListener);
        Sys.smsEventSource.removeEventListener(this.smsChangedEventListener);
        Sys.loginEventSource.removeEventListener(loginChangeEventListener);
    }

    @Override
    public int getIconResourceId() {
        return R.drawable.tab_icon_home;
    }

    public void updateConnectionStatus(boolean isConnected) {
        if (!viewCreated || getView() == null || imgStatus == null) {
            Log.d(TAG, "updateConnectionStatus: View or imgStatus is null. Cannot update. isConnected: " + isConnected);
            return;
        }
        Log.d(TAG, "updateConnectionStatus: called with isConnected: " + isConnected);
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                if (isConnected) {
                    Log.d(TAG, "updateConnectionStatus: Setting icon to online");
                    imgStatus.setImageResource(android.R.drawable.presence_online);
                    imgStatus.setVisibility(View.VISIBLE);
                } else {
                    Log.d(TAG, "updateConnectionStatus: Setting icon to offline");
                    imgStatus.setImageResource(android.R.drawable.presence_offline);
                    imgStatus.setVisibility(View.VISIBLE);
                }
            });
        }
    }

    @Override
    public void onConnectionStatusChanged(boolean isConnected) {
        Log.d(TAG, "onConnectionStatusChanged: Received status: " + isConnected + ". Current mIsSocketConnected: " + mIsSocketConnected);
        this.mIsSocketConnected = isConnected;

        if (!viewCreated || getView() == null || imgStatus == null) {
            Log.d(TAG, "onConnectionStatusChanged: View or imgStatus not ready. Storing in pendingInitialConnectionState: " + isConnected);
            pendingInitialConnectionState = isConnected;
        } else {
            Log.d(TAG, "onConnectionStatusChanged: View ready. Calling updateConnectionStatus directly with: " + isConnected);
            updateConnectionStatus(isConnected);
            pendingInitialConnectionState = null;
            
            // 如果已连接，更新lastPong时间戳
            if (isConnected) {
                lastPong = new Date();
            }
        }
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        Log.d(TAG, "onAttach() called. Registering ConnectionStatusListener.");
        SocketService.registerConnectionStatusListener(this);
        

        if (Sys.isLogin()) {
            SocketService.preInitSocketConnection(context);
        }
    }

    @Override
    public void onDetach() {
        super.onDetach();
        Log.d(TAG, "onDetach() called. Unregistering ConnectionStatusListener.");
        SocketService.unregisterConnectionStatusListener(this);
    }
}
