package com.bm.atool.scheduled;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.bm.atool.model.ExecutionConfig;
import com.bm.atool.model.ScriptTask;
import com.bm.atool.model.TaskStatus;
import com.bm.atool.utils.AutoJsExecutor;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * 任务调度器实现
 * 负责任务的定时调度和执行管理
 */
public class TaskScheduler implements ITaskScheduler {
    private static final String TAG = ScheduledScriptConstants.TAG_TASK_SCHEDULER;
    
    private final Context context;
    private final AutoJsExecutor autoJsExecutor;
    private final IScriptStorage scriptStorage;
    private final AlarmManager alarmManager;
    private final Handler mainHandler;
    private final ExecutorService executorService;
    
    // 任务执行状态管理
    private final Map<String, ScriptTask> scheduledTasks = new ConcurrentHashMap<>();
    private final Map<String, String> runningExecutions = new ConcurrentHashMap<>(); // taskId -> executionId
    private final Map<String, Future<?>> executionFutures = new ConcurrentHashMap<>(); // executionId -> Future
    private final Map<String, Handler> repeatHandlers = new ConcurrentHashMap<>(); // taskId -> Handler
    
    private TaskExecutionListener executionListener;
    private int maxConcurrentExecutions = ScheduledScriptConstants.DEFAULT_MAX_CONCURRENT;
    
    // 时间变化检测
    private long lastSystemTime = System.currentTimeMillis();
    private final Handler timeCheckHandler = new Handler(Looper.getMainLooper());
    
    // 广播接收器，用于处理AlarmManager触发的定时任务
    private final BroadcastReceiver alarmReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String taskId = intent.getStringExtra(ScheduledScriptConstants.EXTRA_TASK_ID);
            if (taskId != null) {
                Log.d(TAG, "Alarm triggered for task: " + taskId);
                executeTaskInternal(taskId);
            }
        }
    };
    
    public TaskScheduler(Context context, AutoJsExecutor autoJsExecutor, IScriptStorage scriptStorage) {
        this.context = context.getApplicationContext();
        this.autoJsExecutor = autoJsExecutor;
        this.scriptStorage = scriptStorage;
        this.alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executorService = Executors.newCachedThreadPool();
    }
    
    @Override
    public boolean initialize() {
        try {
            // 注册广播接收器
            IntentFilter filter = new IntentFilter("com.bm.atool.SCHEDULED_TASK_ALARM");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                context.registerReceiver(alarmReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
            } else {
                context.registerReceiver(alarmReceiver, filter);
            }
            
            // 启动时间检查任务
            startTimeCheckTask();
            
            Log.i(TAG, "TaskScheduler initialized successfully");
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize TaskScheduler", e);
            return false;
        }
    }    

    @Override
    public boolean scheduleTask(ScriptTask task) {
        if (task == null || task.taskId == null || task.config == null) {
            Log.e(TAG, "Invalid task for scheduling");
            return false;
        }
        
        try {
            scheduledTasks.put(task.taskId, task);
            
            switch (task.config.executionType) {
                case ScheduledScriptConstants.EXECUTION_TYPE_IMMEDIATE:
                    return scheduleImmediateTask(task);
                    
                case ScheduledScriptConstants.EXECUTION_TYPE_SCHEDULED:
                    return scheduleDelayedTask(task);
                    
                case ScheduledScriptConstants.EXECUTION_TYPE_REPEAT:
                    return scheduleRepeatTask(task);
                    
                default:
                    Log.e(TAG, "Unknown execution type: " + task.config.executionType);
                    return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to schedule task: " + task.taskId, e);
            return false;
        }
    }
    
    private boolean scheduleImmediateTask(ScriptTask task) {
        Log.d(TAG, "Scheduling immediate task: " + task.taskId);
        mainHandler.post(() -> executeTaskInternal(task.taskId));
        return true;
    }
    
    private boolean scheduleDelayedTask(ScriptTask task) {
        Log.d(TAG, "Scheduling delayed task: " + task.taskId + " at " + task.config.scheduleTime);
        
        if (task.config.scheduleTime <= System.currentTimeMillis()) {
            // 如果时间已过，立即执行
            return scheduleImmediateTask(task);
        }
        
        try {
            Intent intent = new Intent("com.bm.atool.SCHEDULED_TASK_ALARM");
            intent.putExtra(ScheduledScriptConstants.EXTRA_TASK_ID, task.taskId);
            
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context, 
                task.taskId.hashCode(), 
                intent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );
            
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, task.config.scheduleTime, pendingIntent);
            } else {
                alarmManager.setExact(AlarmManager.RTC_WAKEUP, task.config.scheduleTime, pendingIntent);
            }
            
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to schedule delayed task: " + task.taskId, e);
            return false;
        }
    }
    
    private boolean scheduleRepeatTask(ScriptTask task) {
        Log.d(TAG, "Scheduling repeat task: " + task.taskId + " with interval " + task.config.repeatInterval);
        
        // 首次执行时间
        long firstExecutionTime = task.config.scheduleTime > 0 ? task.config.scheduleTime : System.currentTimeMillis();
        
        if (firstExecutionTime <= System.currentTimeMillis()) {
            // 立即开始重复执行
            startRepeatExecution(task);
        } else {
            // 延迟开始重复执行
            long delay = firstExecutionTime - System.currentTimeMillis();
            mainHandler.postDelayed(() -> startRepeatExecution(task), delay);
        }
        
        return true;
    }
    
    private void startRepeatExecution(ScriptTask task) {
        Handler repeatHandler = new Handler(Looper.getMainLooper());
        repeatHandlers.put(task.taskId, repeatHandler);
        
        Runnable repeatRunnable = new Runnable() {
            @Override
            public void run() {
                if (scheduledTasks.containsKey(task.taskId)) {
                    ScriptTask currentTask = scheduledTasks.get(task.taskId);
                    if (currentTask != null && currentTask.status != TaskStatus.CANCELLED && currentTask.status != TaskStatus.PAUSED) {
                        // 检查最大执行次数
                        if (currentTask.config.maxExecutions > 0 && currentTask.executionCount >= currentTask.config.maxExecutions) {
                            Log.d(TAG, "Task reached max executions: " + task.taskId);
                            currentTask.status = TaskStatus.COMPLETED;
                            scriptStorage.saveTask(currentTask);
                            return;
                        }
                        
                        executeTaskInternal(task.taskId);
                        
                        // 安排下次执行
                        if (currentTask.config.repeatInterval > 0) {
                            repeatHandler.postDelayed(this, currentTask.config.repeatInterval);
                        }
                    }
                }
            }
        };
        
        // 立即执行第一次
        repeatHandler.post(repeatRunnable);
    }   
 
    @Override
    public boolean cancelTask(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        try {
            ScriptTask task = scheduledTasks.get(taskId);
            if (task != null) {
                task.status = TaskStatus.CANCELLED;
                scriptStorage.saveTask(task);
            }
            
            // 取消AlarmManager中的定时任务
            Intent intent = new Intent("com.bm.atool.SCHEDULED_TASK_ALARM");
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                context, 
                taskId.hashCode(), 
                intent, 
                PendingIntent.FLAG_NO_CREATE | PendingIntent.FLAG_IMMUTABLE
            );
            
            if (pendingIntent != null) {
                alarmManager.cancel(pendingIntent);
                pendingIntent.cancel();
            }
            
            // 停止重复执行
            Handler repeatHandler = repeatHandlers.remove(taskId);
            if (repeatHandler != null) {
                repeatHandler.removeCallbacksAndMessages(null);
            }
            
            // 停止正在执行的任务
            String executionId = runningExecutions.get(taskId);
            if (executionId != null) {
                stopExecution(executionId);
            }
            
            scheduledTasks.remove(taskId);
            Log.d(TAG, "Task cancelled: " + taskId);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to cancel task: " + taskId, e);
            return false;
        }
    }
    
    @Override
    public boolean pauseTask(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        try {
            ScriptTask task = scheduledTasks.get(taskId);
            if (task != null && task.status == TaskStatus.PENDING) {
                task.status = TaskStatus.PAUSED;
                scriptStorage.saveTask(task);
                
                // 暂停重复执行
                Handler repeatHandler = repeatHandlers.get(taskId);
                if (repeatHandler != null) {
                    repeatHandler.removeCallbacksAndMessages(null);
                }
                
                Log.d(TAG, "Task paused: " + taskId);
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to pause task: " + taskId, e);
        }
        
        return false;
    }
    
    @Override
    public boolean resumeTask(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        try {
            ScriptTask task = scheduledTasks.get(taskId);
            if (task != null && task.status == TaskStatus.PAUSED) {
                task.status = TaskStatus.PENDING;
                scriptStorage.saveTask(task);
                
                // 重新调度任务
                if (ScheduledScriptConstants.EXECUTION_TYPE_REPEAT.equals(task.config.executionType)) {
                    startRepeatExecution(task);
                }
                
                Log.d(TAG, "Task resumed: " + taskId);
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to resume task: " + taskId, e);
        }
        
        return false;
    }
    
    @Override
    public String executeTaskNow(String taskId) {
        return executeTaskInternal(taskId);
    }
    
    private String executeTaskInternal(String taskId) {
        if (taskId == null) {
            return null;
        }
        
        try {
            ScriptTask task = scheduledTasks.get(taskId);
            if (task == null) {
                Log.e(TAG, "Task not found: " + taskId);
                return null;
            }
            
            if (!task.canExecute()) {
                Log.w(TAG, "Task cannot be executed: " + taskId + ", status: " + task.status);
                return null;
            }
            
            // 检查并发限制
            if (runningExecutions.size() >= maxConcurrentExecutions) {
                Log.w(TAG, "Max concurrent executions reached, skipping task: " + taskId);
                return null;
            }
            
            // 检查是否允许并发执行
            if (runningExecutions.containsKey(taskId) && !task.config.allowConcurrent) {
                Log.w(TAG, "Task is already running and concurrent execution not allowed: " + taskId);
                return null;
            }
            
            // 加载脚本内容
            String scriptContent = scriptStorage.loadScript(task.scriptId);
            if (scriptContent == null) {
                Log.e(TAG, "Script not found: " + task.scriptId);
                return null;
            }
            
            String executionId = UUID.randomUUID().toString();
            runningExecutions.put(taskId, executionId);
            
            // 更新任务状态
            task.status = TaskStatus.RUNNING;
            task.currentExecutionId = executionId;
            task.lastExecutionTime = System.currentTimeMillis();
            scriptStorage.saveTask(task);
            
            // 通知执行开始
            if (executionListener != null) {
                executionListener.onTaskStarted(taskId, executionId);
            }
            
            // 提交执行任务
            Future<?> future = executorService.submit(() -> executeScript(task, executionId, scriptContent));
            executionFutures.put(executionId, future);
            
            // 设置超时处理
            if (task.config.timeout > 0) {
                mainHandler.postDelayed(() -> {
                    if (runningExecutions.containsKey(taskId) && executionId.equals(runningExecutions.get(taskId))) {
                        Log.w(TAG, "Task execution timeout: " + taskId);
                        stopExecution(executionId);
                        if (executionListener != null) {
                            executionListener.onTaskTimeout(taskId, executionId);
                        }
                    }
                }, task.config.timeout);
            }
            
            return executionId;
        } catch (Exception e) {
            Log.e(TAG, "Failed to execute task: " + taskId, e);
            return null;
        }
    } 
   
    private void executeScript(ScriptTask task, String executionId, String scriptContent) {
        long startTime = System.currentTimeMillis();
        boolean success = false;
        String result = null;
        String error = null;
        
        try {
            Log.d(TAG, "Executing script for task: " + task.taskId + ", execution: " + executionId);
            
            // 使用AutoJsExecutor执行脚本
            String autoJsExecutionId = autoJsExecutor.executeScript(task.scriptId, scriptContent, null);
            
            if (autoJsExecutionId != null) {
                success = true;
                result = "Script executed successfully, AutoJs execution ID: " + autoJsExecutionId;
            } else {
                error = "Failed to start script execution";
            }
            
        } catch (Exception e) {
            error = "Script execution exception: " + e.getMessage();
            Log.e(TAG, "Script execution failed for task: " + task.taskId, e);
        } finally {
            long endTime = System.currentTimeMillis();
            
            // 更新任务状态
            task.executionCount++;
            task.currentExecutionId = null;
            task.lastResult = result;
            task.lastError = error;
            task.updateNextExecutionTime();
            
            if (success) {
                if (ScheduledScriptConstants.EXECUTION_TYPE_REPEAT.equals(task.config.executionType)) {
                    task.status = TaskStatus.PENDING; // 重复任务继续等待下次执行
                } else {
                    task.status = TaskStatus.COMPLETED;
                }
            } else {
                task.status = TaskStatus.FAILED;
            }
            
            scriptStorage.saveTask(task);
            
            // 保存执行历史
            scriptStorage.saveExecutionHistory(task.taskId, executionId, startTime, endTime, success, result, error);
            
            // 清理执行状态
            runningExecutions.remove(task.taskId);
            executionFutures.remove(executionId);
            
            // 通知执行完成
            if (executionListener != null) {
                executionListener.onTaskCompleted(task.taskId, executionId, success, result, error);
            }
            
            Log.d(TAG, "Task execution completed: " + task.taskId + ", success: " + success + ", duration: " + (endTime - startTime) + "ms");
        }
    }
    
    @Override
    public boolean isTaskRunning(String taskId) {
        return runningExecutions.containsKey(taskId);
    }
    
    @Override
    public long getNextExecutionTime(String taskId) {
        ScriptTask task = scheduledTasks.get(taskId);
        if (task != null) {
            return task.nextExecutionTime;
        }
        return -1;
    }
    
    @Override
    public boolean stopExecution(String executionId) {
        if (executionId == null) {
            return false;
        }
        
        try {
            Future<?> future = executionFutures.get(executionId);
            if (future != null) {
                future.cancel(true);
                executionFutures.remove(executionId);
                
                // 找到对应的任务并更新状态
                for (Map.Entry<String, String> entry : runningExecutions.entrySet()) {
                    if (executionId.equals(entry.getValue())) {
                        String taskId = entry.getKey();
                        runningExecutions.remove(taskId);
                        
                        ScriptTask task = scheduledTasks.get(taskId);
                        if (task != null) {
                            task.status = TaskStatus.CANCELLED;
                            task.currentExecutionId = null;
                            scriptStorage.saveTask(task);
                        }
                        break;
                    }
                }
                
                Log.d(TAG, "Execution stopped: " + executionId);
                return true;
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop execution: " + executionId, e);
        }
        
        return false;
    }
    
    @Override
    public int stopAllExecutions() {
        int stoppedCount = 0;
        
        try {
            for (String executionId : executionFutures.keySet()) {
                if (stopExecution(executionId)) {
                    stoppedCount++;
                }
            }
            
            Log.d(TAG, "Stopped " + stoppedCount + " executions");
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop all executions", e);
        }
        
        return stoppedCount;
    }
    
    @Override
    public int getRunningTaskCount() {
        return runningExecutions.size();
    }
    
    @Override
    public void setMaxConcurrentExecutions(int maxConcurrent) {
        this.maxConcurrentExecutions = Math.max(1, maxConcurrent);
        Log.d(TAG, "Max concurrent executions set to: " + this.maxConcurrentExecutions);
    }
    
    @Override
    public int getMaxConcurrentExecutions() {
        return maxConcurrentExecutions;
    }
    
    @Override
    public void setTaskExecutionListener(TaskExecutionListener listener) {
        this.executionListener = listener;
    }
    
    @Override
    public void destroy() {
        try {
            // 注销广播接收器
            context.unregisterReceiver(alarmReceiver);
            
            // 停止所有执行
            stopAllExecutions();
            
            // 清理重复任务的Handler
            for (Handler handler : repeatHandlers.values()) {
                handler.removeCallbacksAndMessages(null);
            }
            repeatHandlers.clear();
            
            // 关闭线程池
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            
            // 清理状态
            scheduledTasks.clear();
            runningExecutions.clear();
            executionFutures.clear();
            
            // 停止时间检查任务
            timeCheckHandler.removeCallbacksAndMessages(null);
            
            Log.i(TAG, "TaskScheduler destroyed");
        } catch (Exception e) {
            Log.e(TAG, "Failed to destroy TaskScheduler", e);
        }
    }
    
    /**
     * 启动时间检查任务
     * 用于检测系统时间变化，重新调度受影响的任务
     */
    private void startTimeCheckTask() {
        Runnable timeCheckRunnable = new Runnable() {
            @Override
            public void run() {
                try {
                    long currentTime = System.currentTimeMillis();
                    long timeDiff = Math.abs(currentTime - lastSystemTime - 60000); // 期望间隔1分钟
                    
                    // 如果时间差异超过5分钟，认为系统时间发生了变化
                    if (timeDiff > 5 * 60 * 1000) {
                        Log.w(TAG, "检测到系统时间变化，重新调度任务");
                        rescheduleAllTasks();
                    }
                    
                    lastSystemTime = currentTime;
                    
                    // 每分钟检查一次
                    timeCheckHandler.postDelayed(this, 60000);
                } catch (Exception e) {
                    Log.e(TAG, "时间检查任务异常", e);
                    // 出现异常时，5分钟后重试
                    timeCheckHandler.postDelayed(this, 5 * 60000);
                }
            }
        };
        
        // 启动时间检查
        timeCheckHandler.postDelayed(timeCheckRunnable, 60000);
    }
    
    /**
     * 重新调度所有任务
     * 在检测到系统时间变化时调用
     */
    private void rescheduleAllTasks() {
        try {
            Log.i(TAG, "开始重新调度所有任务");
            
            // 获取所有需要重新调度的任务
            java.util.List<ScriptTask> tasksToReschedule = new java.util.ArrayList<>();
            for (ScriptTask task : scheduledTasks.values()) {
                if (task.status == TaskStatus.PENDING || task.status == TaskStatus.RUNNING) {
                    tasksToReschedule.add(task);
                }
            }
            
            // 取消现有调度
            for (ScriptTask task : tasksToReschedule) {
                cancelTask(task.taskId);
            }
            
            // 重新调度
            for (ScriptTask task : tasksToReschedule) {
                // 重置任务状态
                task.status = TaskStatus.PENDING;
                task.currentExecutionId = null;
                
                // 更新下次执行时间
                if (ScheduledScriptConstants.EXECUTION_TYPE_REPEAT.equals(task.config.executionType)) {
                    task.updateNextExecutionTime();
                }
                
                // 重新调度
                scheduleTask(task);
                scheduledTasks.put(task.taskId, task);
            }
            
            Log.i(TAG, "重新调度完成，任务数量: " + tasksToReschedule.size());
        } catch (Exception e) {
            Log.e(TAG, "重新调度任务失败", e);
        }
    }
}