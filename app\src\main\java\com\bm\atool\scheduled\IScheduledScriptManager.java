package com.bm.atool.scheduled;

import com.bm.atool.model.ExecutionConfig;
import com.bm.atool.model.ScheduledScriptRequest;
import com.bm.atool.model.ScriptTask;

import java.util.List;

/**
 * 定时脚本管理器接口
 * 负责脚本和任务的生命周期管理
 */
public interface IScheduledScriptManager {
    
    /**
     * 添加脚本
     * @param request 脚本请求
     * @return 脚本ID，如果添加失败返回null
     */
    String addScript(ScheduledScriptRequest request);
    
    /**
     * 移除脚本及其所有相关任务
     * @param scriptId 脚本ID
     * @return 是否成功移除
     */
    boolean removeScript(String scriptId);
    
    /**
     * 更新脚本内容
     * @param scriptId 脚本ID
     * @param request 新的脚本请求
     * @return 是否成功更新
     */
    boolean updateScript(String scriptId, ScheduledScriptRequest request);
    
    /**
     * 检查脚本是否存在
     * @param scriptId 脚本ID
     * @return 是否存在
     */
    boolean scriptExists(String scriptId);
    
    /**
     * 创建并调度任务
     * @param scriptId 脚本ID
     * @param config 执行配置
     * @return 任务ID，如果创建失败返回null
     */
    String scheduleTask(String scriptId, ExecutionConfig config);
    
    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancelTask(String taskId);
    
    /**
     * 暂停任务
     * @param taskId 任务ID
     * @return 是否成功暂停
     */
    boolean pauseTask(String taskId);
    
    /**
     * 恢复任务
     * @param taskId 任务ID
     * @return 是否成功恢复
     */
    boolean resumeTask(String taskId);
    
    /**
     * 立即执行任务
     * @param taskId 任务ID
     * @return 执行ID，如果执行失败返回null
     */
    String executeTaskNow(String taskId);
    
    /**
     * 获取所有任务
     * @return 任务列表
     */
    List<ScriptTask> getAllTasks();
    
    /**
     * 获取指定任务
     * @param taskId 任务ID
     * @return 任务对象，如果不存在返回null
     */
    ScriptTask getTask(String taskId);
    
    /**
     * 获取指定脚本的所有任务
     * @param scriptId 脚本ID
     * @return 任务列表
     */
    List<ScriptTask> getTasksByScript(String scriptId);
    
    /**
     * 获取指定状态的任务
     * @param status 任务状态
     * @return 任务列表
     */
    List<ScriptTask> getTasksByStatus(com.bm.atool.model.TaskStatus status);
    
    /**
     * 清理已完成的任务
     * @param olderThanMs 清理多少毫秒前的任务
     * @return 清理的任务数量
     */
    int cleanupCompletedTasks(long olderThanMs);
    
    /**
     * 初始化管理器
     * 恢复持久化的任务等
     */
    void initialize();
    
    /**
     * 销毁管理器
     * 清理资源，停止所有任务
     */
    void destroy();
}