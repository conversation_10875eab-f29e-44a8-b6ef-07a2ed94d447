package com.bm.atool.scheduled;

import com.bm.atool.model.ScriptTask;

/**
 * 任务调度器接口
 * 负责任务的定时调度和执行管理
 */
public interface ITaskScheduler {
    
    /**
     * 调度任务
     * @param task 要调度的任务
     * @return 是否调度成功
     */
    boolean scheduleTask(ScriptTask task);
    
    /**
     * 取消任务调度
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelTask(String taskId);
    
    /**
     * 暂停任务
     * @param taskId 任务ID
     * @return 是否暂停成功
     */
    boolean pauseTask(String taskId);
    
    /**
     * 恢复任务
     * @param taskId 任务ID
     * @return 是否恢复成功
     */
    boolean resumeTask(String taskId);
    
    /**
     * 立即执行任务
     * @param taskId 任务ID
     * @return 执行ID，如果执行失败返回null
     */
    String executeTaskNow(String taskId);
    
    /**
     * 检查任务是否正在运行
     * @param taskId 任务ID
     * @return 是否正在运行
     */
    boolean isTaskRunning(String taskId);
    
    /**
     * 获取任务的下次执行时间
     * @param taskId 任务ID
     * @return 下次执行时间戳，如果任务不存在或不是定时任务返回-1
     */
    long getNextExecutionTime(String taskId);
    
    /**
     * 停止指定执行
     * @param executionId 执行ID
     * @return 是否停止成功
     */
    boolean stopExecution(String executionId);
    
    /**
     * 停止所有执行
     * @return 停止的执行数量
     */
    int stopAllExecutions();
    
    /**
     * 获取当前运行的任务数量
     * @return 运行中的任务数量
     */
    int getRunningTaskCount();
    
    /**
     * 设置最大并发执行数
     * @param maxConcurrent 最大并发数
     */
    void setMaxConcurrentExecutions(int maxConcurrent);
    
    /**
     * 获取最大并发执行数
     * @return 最大并发数
     */
    int getMaxConcurrentExecutions();
    
    /**
     * 初始化调度器
     * @return 是否初始化成功
     */
    boolean initialize();
    
    /**
     * 销毁调度器
     * 停止所有任务并清理资源
     */
    void destroy();
    
    /**
     * 任务执行监听器
     */
    interface TaskExecutionListener {
        /**
         * 任务开始执行
         * @param taskId 任务ID
         * @param executionId 执行ID
         */
        void onTaskStarted(String taskId, String executionId);
        
        /**
         * 任务执行完成
         * @param taskId 任务ID
         * @param executionId 执行ID
         * @param success 是否成功
         * @param result 执行结果
         * @param error 错误信息
         */
        void onTaskCompleted(String taskId, String executionId, boolean success, String result, String error);
        
        /**
         * 任务执行超时
         * @param taskId 任务ID
         * @param executionId 执行ID
         */
        void onTaskTimeout(String taskId, String executionId);
    }
    
    /**
     * 设置任务执行监听器
     * @param listener 监听器
     */
    void setTaskExecutionListener(TaskExecutionListener listener);
}