package com.bm.atool.scheduled;

import android.content.Context;
import android.util.Log;

import com.bm.atool.model.ExecutionConfig;
import com.bm.atool.model.ScheduledScriptRequest;
import com.bm.atool.model.ScriptTask;
import com.bm.atool.model.TaskStatus;
import com.bm.atool.utils.AutoJsExecutor;
import com.google.gson.Gson;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 定时脚本管理器实现
 * 作为整个定时脚本系统的核心管理器
 */
public class ScheduledScriptManager implements IScheduledScriptManager, ITaskScheduler.TaskExecutionListener {
    private static final String TAG = ScheduledScriptConstants.TAG_SCRIPT_MANAGER;
    
    private static ScheduledScriptManager instance;
    private final Context context;
    private final IScriptStorage scriptStorage;
    private final ITaskScheduler taskScheduler;
    private final AutoJsExecutor autoJsExecutor;
    private final Gson gson;
    private final ScheduledExecutorService cleanupExecutor;
    
    // 脚本和任务缓存
    private final ConcurrentHashMap<String, ScheduledScriptRequest> scriptCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, ScriptTask> taskCache = new ConcurrentHashMap<>();
    
    // 离线结果缓存
    private final java.util.Queue<String> offlineResultsQueue = new java.util.concurrent.ConcurrentLinkedQueue<>();
    
    private boolean initialized = false;
    
    private ScheduledScriptManager(Context context) {
        this.context = context.getApplicationContext();
        this.scriptStorage = new FileBasedScriptStorage(context);
        this.autoJsExecutor = AutoJsExecutor.getInstance(context);
        this.taskScheduler = new TaskScheduler(context, autoJsExecutor, scriptStorage);
        this.gson = new Gson();
        this.cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
    }
    
    /**
     * 获取单例实例
     */
    public static synchronized ScheduledScriptManager getInstance(Context context) {
        if (instance == null) {
            instance = new ScheduledScriptManager(context);
        }
        return instance;
    }
    
    @Override
    public void initialize() {
        if (initialized) {
            Log.w(TAG, "ScheduledScriptManager already initialized");
            return;
        }
        
        try {
            Log.i(TAG, "Initializing ScheduledScriptManager...");
            
            // 初始化存储
            if (!scriptStorage.initialize()) {
                throw new RuntimeException("Failed to initialize script storage");
            }
            
            // 初始化任务调度器
            if (!taskScheduler.initialize()) {
                throw new RuntimeException("Failed to initialize task scheduler");
            }
            
            // 设置任务执行监听器
            taskScheduler.setTaskExecutionListener(this);
            
            // 恢复持久化任务
            restorePersistentTasks();
            
            // 启动定期清理任务
            startCleanupTask();
            
            // 注册网络状态监听
            registerNetworkListener();
            
            initialized = true;
            Log.i(TAG, "ScheduledScriptManager initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize ScheduledScriptManager", e);
            throw new RuntimeException("ScheduledScriptManager initialization failed", e);
        }
    }
    
    private void restorePersistentTasks() {
        try {
            List<ScriptTask> persistentTasks = scriptStorage.loadPersistentTasks();
            Log.i(TAG, "Restoring " + persistentTasks.size() + " persistent tasks");
            
            for (ScriptTask task : persistentTasks) {
                // 重置运行状态
                if (task.status == TaskStatus.RUNNING) {
                    task.status = TaskStatus.PENDING;
                    task.currentExecutionId = null;
                }
                
                // 更新下次执行时间
                if (ScheduledScriptConstants.EXECUTION_TYPE_REPEAT.equals(task.config.executionType)) {
                    task.updateNextExecutionTime();
                }
                
                taskCache.put(task.taskId, task);
                
                // 重新调度任务
                if (task.status == TaskStatus.PENDING) {
                    taskScheduler.scheduleTask(task);
                }
            }
            
            Log.i(TAG, "Restored " + persistentTasks.size() + " persistent tasks");
        } catch (Exception e) {
            Log.e(TAG, "Failed to restore persistent tasks", e);
        }
    }
    
    private void startCleanupTask() {
        // 每24小时执行一次清理任务
        cleanupExecutor.scheduleAtFixedRate(() -> {
            try {
                Log.d(TAG, "Starting scheduled cleanup...");
                int cleanedTasks = cleanupCompletedTasks(ScheduledScriptConstants.DEFAULT_HISTORY_RETENTION_MS);
                int cleanedFiles = scriptStorage.cleanup();
                Log.i(TAG, "Cleanup completed: " + cleanedTasks + " tasks, " + cleanedFiles + " files");
            } catch (Exception e) {
                Log.e(TAG, "Cleanup task failed", e);
            }
        }, 1, 24, TimeUnit.HOURS);
    }  
  
    // ========== 脚本管理方法 ==========
    
    @Override
    public String addScript(ScheduledScriptRequest request) {
        if (request == null) {
            Log.e(TAG, "Script request is null");
            return null;
        }
        
        try {
            // 生成脚本ID（如果没有提供）
            if (request.scriptId == null || request.scriptId.isEmpty()) {
                request.scriptId = UUID.randomUUID().toString();
            }
            
            // 验证脚本内容
            if (request.scriptContent == null || request.scriptContent.trim().isEmpty()) {
                Log.e(TAG, "Script content is empty");
                return null;
            }
            
            // 保存脚本内容
            if (!scriptStorage.saveScript(request.scriptId, request.scriptContent)) {
                Log.e(TAG, "Failed to save script content");
                return null;
            }
            
            // 保存脚本元数据
            String metadata = gson.toJson(request);
            if (!scriptStorage.saveScriptMetadata(request.scriptId, metadata)) {
                Log.w(TAG, "Failed to save script metadata, but script content saved");
            }
            
            // 缓存脚本信息
            scriptCache.put(request.scriptId, request);
            
            // 如果有执行配置，创建并调度任务
            if (request.config != null) {
                String taskId = scheduleTask(request.scriptId, request.config);
                if (taskId != null) {
                    Log.i(TAG, "Script added and task scheduled: " + request.scriptId + " -> " + taskId);
                } else {
                    Log.w(TAG, "Script added but task scheduling failed: " + request.scriptId);
                }
            }
            
            Log.i(TAG, "Script added successfully: " + request.scriptId);
            return request.scriptId;
        } catch (Exception e) {
            Log.e(TAG, "Failed to add script", e);
            return null;
        }
    }
    
    @Override
    public boolean removeScript(String scriptId) {
        if (scriptId == null) {
            return false;
        }
        
        try {
            // 取消所有相关任务
            List<ScriptTask> relatedTasks = getTasksByScript(scriptId);
            for (ScriptTask task : relatedTasks) {
                taskScheduler.cancelTask(task.taskId);
                scriptStorage.deleteTask(task.taskId);
                taskCache.remove(task.taskId);
            }
            
            // 删除脚本文件
            boolean scriptDeleted = scriptStorage.deleteScript(scriptId);
            
            // 清理缓存
            scriptCache.remove(scriptId);
            
            Log.i(TAG, "Script removed: " + scriptId + ", tasks cancelled: " + relatedTasks.size());
            return scriptDeleted;
        } catch (Exception e) {
            Log.e(TAG, "Failed to remove script: " + scriptId, e);
            return false;
        }
    }
    
    @Override
    public boolean updateScript(String scriptId, ScheduledScriptRequest request) {
        if (scriptId == null || request == null) {
            return false;
        }
        
        try {
            // 更新脚本内容
            if (request.scriptContent != null) {
                if (!scriptStorage.saveScript(scriptId, request.scriptContent)) {
                    Log.e(TAG, "Failed to update script content");
                    return false;
                }
            }
            
            // 更新元数据
            request.scriptId = scriptId; // 确保ID一致
            String metadata = gson.toJson(request);
            scriptStorage.saveScriptMetadata(scriptId, metadata);
            
            // 更新缓存
            scriptCache.put(scriptId, request);
            
            Log.i(TAG, "Script updated: " + scriptId);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to update script: " + scriptId, e);
            return false;
        }
    }
    
    @Override
    public boolean scriptExists(String scriptId) {
        return scriptId != null && scriptStorage.scriptExists(scriptId);
    } 
   
    // ========== 任务管理方法 ==========
    
    @Override
    public String scheduleTask(String scriptId, ExecutionConfig config) {
        if (scriptId == null || config == null) {
            Log.e(TAG, "Script ID or config is null");
            return null;
        }
        
        if (!scriptExists(scriptId)) {
            Log.e(TAG, "Script not found: " + scriptId);
            return null;
        }
        
        try {
            // 创建任务
            ScriptTask task = new ScriptTask();
            task.taskId = UUID.randomUUID().toString();
            task.scriptId = scriptId;
            task.config = config;
            task.status = TaskStatus.PENDING;
            task.createdTime = System.currentTimeMillis();
            
            // 设置下次执行时间
            if (ScheduledScriptConstants.EXECUTION_TYPE_SCHEDULED.equals(config.executionType) ||
                ScheduledScriptConstants.EXECUTION_TYPE_REPEAT.equals(config.executionType)) {
                task.nextExecutionTime = config.scheduleTime > 0 ? config.scheduleTime : System.currentTimeMillis();
            }
            
            // 保存任务
            if (!scriptStorage.saveTask(task)) {
                Log.e(TAG, "Failed to save task");
                return null;
            }
            
            // 缓存任务
            taskCache.put(task.taskId, task);
            
            // 调度任务
            if (!taskScheduler.scheduleTask(task)) {
                Log.e(TAG, "Failed to schedule task");
                scriptStorage.deleteTask(task.taskId);
                taskCache.remove(task.taskId);
                return null;
            }
            
            Log.i(TAG, "Task scheduled: " + task.taskId + " for script: " + scriptId);
            return task.taskId;
        } catch (Exception e) {
            Log.e(TAG, "Failed to schedule task for script: " + scriptId, e);
            return null;
        }
    }
    
    @Override
    public boolean cancelTask(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        try {
            boolean cancelled = taskScheduler.cancelTask(taskId);
            if (cancelled) {
                scriptStorage.deleteTask(taskId);
                taskCache.remove(taskId);
                Log.i(TAG, "Task cancelled: " + taskId);
            }
            return cancelled;
        } catch (Exception e) {
            Log.e(TAG, "Failed to cancel task: " + taskId, e);
            return false;
        }
    }
    
    @Override
    public boolean pauseTask(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        try {
            boolean paused = taskScheduler.pauseTask(taskId);
            if (paused) {
                ScriptTask task = taskCache.get(taskId);
                if (task != null) {
                    task.status = TaskStatus.PAUSED;
                    scriptStorage.saveTask(task);
                }
                Log.i(TAG, "Task paused: " + taskId);
            }
            return paused;
        } catch (Exception e) {
            Log.e(TAG, "Failed to pause task: " + taskId, e);
            return false;
        }
    }
    
    @Override
    public boolean resumeTask(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        try {
            boolean resumed = taskScheduler.resumeTask(taskId);
            if (resumed) {
                ScriptTask task = taskCache.get(taskId);
                if (task != null) {
                    task.status = TaskStatus.PENDING;
                    scriptStorage.saveTask(task);
                }
                Log.i(TAG, "Task resumed: " + taskId);
            }
            return resumed;
        } catch (Exception e) {
            Log.e(TAG, "Failed to resume task: " + taskId, e);
            return false;
        }
    }
    
    @Override
    public String executeTaskNow(String taskId) {
        if (taskId == null) {
            return null;
        }
        
        try {
            String executionId = taskScheduler.executeTaskNow(taskId);
            if (executionId != null) {
                Log.i(TAG, "Task executed immediately: " + taskId + " -> " + executionId);
            }
            return executionId;
        } catch (Exception e) {
            Log.e(TAG, "Failed to execute task immediately: " + taskId, e);
            return null;
        }
    }    
 
   // ========== 查询方法 ==========
    
    @Override
    public List<ScriptTask> getAllTasks() {
        try {
            // 从缓存获取最新状态
            List<ScriptTask> tasks = new ArrayList<>(taskCache.values());
            
            // 如果缓存为空，从存储加载
            if (tasks.isEmpty()) {
                tasks = scriptStorage.loadAllTasks();
                for (ScriptTask task : tasks) {
                    taskCache.put(task.taskId, task);
                }
            }
            
            return tasks;
        } catch (Exception e) {
            Log.e(TAG, "Failed to get all tasks", e);
            return new ArrayList<>();
        }
    }
    
    @Override
    public ScriptTask getTask(String taskId) {
        if (taskId == null) {
            return null;
        }
        
        try {
            // 先从缓存获取
            ScriptTask task = taskCache.get(taskId);
            if (task == null) {
                // 从存储加载
                task = scriptStorage.loadTask(taskId);
                if (task != null) {
                    taskCache.put(taskId, task);
                }
            }
            return task;
        } catch (Exception e) {
            Log.e(TAG, "Failed to get task: " + taskId, e);
            return null;
        }
    }
    
    @Override
    public List<ScriptTask> getTasksByScript(String scriptId) {
        List<ScriptTask> result = new ArrayList<>();
        
        if (scriptId == null) {
            return result;
        }
        
        try {
            List<ScriptTask> allTasks = getAllTasks();
            for (ScriptTask task : allTasks) {
                if (scriptId.equals(task.scriptId)) {
                    result.add(task);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to get tasks by script: " + scriptId, e);
        }
        
        return result;
    }
    
    @Override
    public List<ScriptTask> getTasksByStatus(TaskStatus status) {
        List<ScriptTask> result = new ArrayList<>();
        
        if (status == null) {
            return result;
        }
        
        try {
            List<ScriptTask> allTasks = getAllTasks();
            for (ScriptTask task : allTasks) {
                if (status == task.status) {
                    result.add(task);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to get tasks by status: " + status, e);
        }
        
        return result;
    }
    
    @Override
    public int cleanupCompletedTasks(long olderThanMs) {
        int cleanedCount = 0;
        long cutoffTime = System.currentTimeMillis() - olderThanMs;
        
        try {
            List<ScriptTask> allTasks = getAllTasks();
            for (ScriptTask task : allTasks) {
                if (task.status.isTerminal() && task.createdTime < cutoffTime) {
                    if (cancelTask(task.taskId)) {
                        cleanedCount++;
                    }
                }
            }
            
            Log.i(TAG, "Cleaned up " + cleanedCount + " completed tasks");
        } catch (Exception e) {
            Log.e(TAG, "Failed to cleanup completed tasks", e);
        }
        
        return cleanedCount;
    }  
  
    // ========== 任务执行监听器实现 ==========
    
    @Override
    public void onTaskStarted(String taskId, String executionId) {
        try {
            ScriptTask task = taskCache.get(taskId);
            if (task != null) {
                task.status = TaskStatus.RUNNING;
                task.currentExecutionId = executionId;
                task.lastExecutionTime = System.currentTimeMillis();
                scriptStorage.saveTask(task);
                
                Log.d(TAG, "Task started: " + taskId + " -> " + executionId);
                
                // 发送广播通知
                android.content.Intent intent = new android.content.Intent(ScheduledScriptConstants.ACTION_TASK_STARTED);
                intent.putExtra(ScheduledScriptConstants.EXTRA_TASK_ID, taskId);
                intent.putExtra(ScheduledScriptConstants.EXTRA_EXECUTION_ID, executionId);
                context.sendBroadcast(intent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to handle task started event: " + taskId, e);
        }
    }
    
    @Override
    public void onTaskCompleted(String taskId, String executionId, boolean success, String result, String error) {
        try {
            ScriptTask task = taskCache.get(taskId);
            if (task != null) {
                task.executionCount++;
                task.currentExecutionId = null;
                task.lastResult = result;
                task.lastError = error;
                
                if (success) {
                    if (ScheduledScriptConstants.EXECUTION_TYPE_REPEAT.equals(task.config.executionType)) {
                        // 重复任务继续等待下次执行
                        task.status = TaskStatus.PENDING;
                        task.updateNextExecutionTime();
                    } else {
                        // 单次任务完成
                        task.status = TaskStatus.COMPLETED;
                    }
                } else {
                    task.status = TaskStatus.FAILED;
                }
                
                scriptStorage.saveTask(task);
                
                Log.d(TAG, "Task completed: " + taskId + " -> " + executionId + ", success: " + success);
                
                // 发送广播通知
                android.content.Intent intent = new android.content.Intent(
                    success ? ScheduledScriptConstants.ACTION_TASK_COMPLETED : ScheduledScriptConstants.ACTION_TASK_FAILED
                );
                intent.putExtra(ScheduledScriptConstants.EXTRA_TASK_ID, taskId);
                intent.putExtra(ScheduledScriptConstants.EXTRA_EXECUTION_ID, executionId);
                intent.putExtra(ScheduledScriptConstants.EXTRA_SUCCESS, success);
                intent.putExtra(ScheduledScriptConstants.EXTRA_RESULT, result);
                intent.putExtra(ScheduledScriptConstants.EXTRA_ERROR, error);
                context.sendBroadcast(intent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to handle task completed event: " + taskId, e);
        }
    }
    
    @Override
    public void onTaskTimeout(String taskId, String executionId) {
        try {
            ScriptTask task = taskCache.get(taskId);
            if (task != null) {
                task.status = TaskStatus.FAILED;
                task.currentExecutionId = null;
                task.lastError = "Execution timeout";
                scriptStorage.saveTask(task);
                
                Log.w(TAG, "Task timeout: " + taskId + " -> " + executionId);
                
                // 发送广播通知
                android.content.Intent intent = new android.content.Intent(ScheduledScriptConstants.ACTION_TASK_FAILED);
                intent.putExtra(ScheduledScriptConstants.EXTRA_TASK_ID, taskId);
                intent.putExtra(ScheduledScriptConstants.EXTRA_EXECUTION_ID, executionId);
                intent.putExtra(ScheduledScriptConstants.EXTRA_ERROR, "Execution timeout");
                context.sendBroadcast(intent);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to handle task timeout event: " + taskId, e);
        }
    }
    
    // ========== 生命周期管理 ==========
    
    @Override
    public void destroy() {
        if (!initialized) {
            return;
        }
        
        try {
            Log.i(TAG, "Destroying ScheduledScriptManager...");
            
            // 停止清理任务
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            
            // 销毁任务调度器
            taskScheduler.destroy();
            
            // 销毁存储
            scriptStorage.destroy();
            
            // 清理缓存
            scriptCache.clear();
            taskCache.clear();
            
            initialized = false;
            Log.i(TAG, "ScheduledScriptManager destroyed");
        } catch (Exception e) {
            Log.e(TAG, "Failed to destroy ScheduledScriptManager", e);
        }
    }
    
    /**
     * 获取管理器状态信息
     */
    public String getStatusInfo() {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("ScheduledScriptManager Status:\n");
            sb.append("- Initialized: ").append(initialized).append("\n");
            sb.append("- Scripts in cache: ").append(scriptCache.size()).append("\n");
            sb.append("- Tasks in cache: ").append(taskCache.size()).append("\n");
            sb.append("- Running tasks: ").append(taskScheduler.getRunningTaskCount()).append("\n");
            sb.append("- Max concurrent: ").append(taskScheduler.getMaxConcurrentExecutions()).append("\n");
            sb.append("- Storage usage: ").append(scriptStorage.getStorageUsage()).append(" bytes\n");
            
            return sb.toString();
        } catch (Exception e) {
            return "Failed to get status info: " + e.getMessage();
        }
    }
    
    /**
     * 检查管理器是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 注册网络状态监听器
     */
    private void registerNetworkListener() {
        try {
            android.content.IntentFilter filter = new android.content.IntentFilter();
            filter.addAction(android.net.ConnectivityManager.CONNECTIVITY_ACTION);
            
            android.content.BroadcastReceiver networkReceiver = new android.content.BroadcastReceiver() {
                @Override
                public void onReceive(android.content.Context context, android.content.Intent intent) {
                    if (android.net.ConnectivityManager.CONNECTIVITY_ACTION.equals(intent.getAction())) {
                        checkNetworkAndSendCachedResults();
                    }
                }
            };
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
                context.registerReceiver(networkReceiver, filter, android.content.Context.RECEIVER_NOT_EXPORTED);
            } else {
                context.registerReceiver(networkReceiver, filter);
            }
            
            Log.d(TAG, "网络状态监听器注册成功");
        } catch (Exception e) {
            Log.e(TAG, "注册网络状态监听器失败", e);
        }
    }
    
    /**
     * 检查网络状态并发送缓存的结果
     */
    private void checkNetworkAndSendCachedResults() {
        try {
            android.net.ConnectivityManager cm = (android.net.ConnectivityManager) 
                context.getSystemService(android.content.Context.CONNECTIVITY_SERVICE);
            
            if (cm != null) {
                android.net.NetworkInfo networkInfo = cm.getActiveNetworkInfo();
                boolean isConnected = networkInfo != null && networkInfo.isConnected();
                
                if (isConnected && !offlineResultsQueue.isEmpty()) {
                    Log.i(TAG, "网络恢复，发送缓存的执行结果，数量: " + offlineResultsQueue.size());
                    sendCachedResults();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "检查网络状态失败", e);
        }
    }
    
    /**
     * 发送缓存的执行结果
     */
    private void sendCachedResults() {
        try {
            while (!offlineResultsQueue.isEmpty()) {
                String cachedResult = offlineResultsQueue.poll();
                if (cachedResult != null) {
                    // 这里应该通过Socket发送结果
                    // 由于ScheduledScriptManager没有直接的Socket引用，
                    // 可以通过广播或其他方式通知SocketService发送
                    android.content.Intent intent = new android.content.Intent("com.bm.atool.SEND_CACHED_RESULT");
                    intent.putExtra("result", cachedResult);
                    context.sendBroadcast(intent);
                }
            }
            Log.i(TAG, "缓存结果发送完成");
        } catch (Exception e) {
            Log.e(TAG, "发送缓存结果失败", e);
        }
    }
    
    /**
     * 缓存执行结果（当网络不可用时）
     * @param result 执行结果JSON字符串
     */
    public void cacheExecutionResult(String result) {
        try {
            offlineResultsQueue.offer(result);
            Log.d(TAG, "执行结果已缓存，当前缓存数量: " + offlineResultsQueue.size());
            
            // 限制缓存大小，避免内存溢出
            while (offlineResultsQueue.size() > 100) {
                offlineResultsQueue.poll();
                Log.w(TAG, "缓存队列已满，丢弃最旧的结果");
            }
        } catch (Exception e) {
            Log.e(TAG, "缓存执行结果失败", e);
        }
    }
}