package com.bm.atool.utils;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * 脚本广播工具类
 * 用于发送模拟脚本下载和执行的广播
 */
public class ScriptBroadcastUtil {
    
    private static final String TAG = "ScriptBroadcastUtil";
    
    /**
     * 模拟脚本的广播Action
     */
    public static final String ACTION_SIMULATE_SCRIPT = "com.bm.atool.SIMULATE_SCRIPT";
    
    /**
     * 发送模拟脚本广播，触发脚本下载和执行
     * @param context 上下文
     */
    public static void sendSimulateScriptBroadcast(Context context) {
        if (context == null) {
            Log.e(TAG, "context为空，无法发送广播");
            return;
        }
        
        try {
            Intent intent = new Intent(ACTION_SIMULATE_SCRIPT);
            context.sendBroadcast(intent);
            Log.d(TAG, "已发送模拟脚本广播");
        } catch (Exception e) {
            Log.e(TAG, "发送模拟脚本广播失败", e);
        }
    }
} 