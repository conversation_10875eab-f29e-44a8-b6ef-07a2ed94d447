package com.bm.atool;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.bm.atool.scheduled.ScriptDownloader;
import com.bm.atool.scheduled.ScheduledScriptManager;
import com.bm.atool.utils.AutoJsExecutor;
import com.google.android.material.tabs.TabLayout;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@RunWith(AndroidJUnit4.class)
public class ScriptDownloadAndNavigationTest {
    private static final String TAG = "ScriptDownloadNavTest";
    private Context context;
    private ScriptDownloader scriptDownloader;
    private ScheduledScriptManager scriptManager;
    private AutoJsExecutor autoJsExecutor;
    
    // Script execution tracking
    private AtomicBoolean scriptExecutionCompleted = new AtomicBoolean(false);
    private AtomicBoolean scriptExecutionSuccess = new AtomicBoolean(false);
    
    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        scriptDownloader = new ScriptDownloader(context);
        scriptManager = ScheduledScriptManager.getInstance(context);
        autoJsExecutor = AutoJsExecutor.getInstance(context);
        
        // Initialize script manager if needed
        if (!scriptManager.isInitialized()) {
            scriptManager.initialize();
        }
    }
    
    @Test
    public void testScriptDownloadAndNavigation() throws InterruptedException {
        Log.d(TAG, "开始测试脚本下载和导航栏点击");
        
        // 显示开始Toast
        showToast("开始测试脚本下载和导航栏点击");
        
        // 步骤1: 模拟下载脚本（创建本地测试脚本）
        String scriptContent = createTestScript();
        if (scriptContent == null) {
            Log.e(TAG, "脚本创建失败");
            showToast("脚本创建失败");
            return;
        }
        
        Log.d(TAG, "脚本创建成功，长度: " + scriptContent.length());
        showToast("脚本创建成功");
        
        // 步骤2: 执行脚本并等待完成
        boolean executed = executeScriptAndWait(scriptContent);
        if (!executed) {
            Log.e(TAG, "脚本执行失败");
            showToast("脚本执行失败");
            return;
        }
        
        // 步骤3: 等待5秒后开始点击导航栏
        Log.d(TAG, "等待5秒后开始点击导航栏...");
        showToast("脚本执行完成，5秒后开始点击导航栏");
        Thread.sleep(5000);
        
        // 步骤4: 启动MainActivity并反复点击导航栏
        clickNavigationTabsRepeatedly();
        
        // 测试完成
        Log.d(TAG, "测试完成");
        showToast("测试完成");
    }
    
    /**
     * 创建测试脚本内容（模拟下载成功）
     */
    private String createTestScript() {
        try {
            // 创建一个简单的测试脚本
            StringBuilder script = new StringBuilder();
            script.append("console.log('测试脚本开始执行');\n");
            script.append("sleep(1000);\n");  // 模拟1秒的执行时间
            script.append("console.log('测试脚本执行中...');\n");
            script.append("sleep(1000);\n");  // 再模拟1秒的执行时间
            script.append("console.log('测试脚本执行完成');\n");
            
            Log.d(TAG, "测试脚本创建成功");
            showToast("测试脚本创建成功");
            return script.toString();
        } catch (Exception e) {
            Log.e(TAG, "创建测试脚本时出错: " + e.getMessage(), e);
            showToast("创建测试脚本时出错: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 执行脚本并等待完成
     */
    private boolean executeScriptAndWait(String scriptContent) throws InterruptedException {
        try {
            Log.d(TAG, "开始执行脚本");
            showToast("开始执行脚本");
            
            // 重置执行状态
            scriptExecutionCompleted.set(false);
            scriptExecutionSuccess.set(false);
            
            // 使用AutoJsExecutor执行脚本
            String executionId = autoJsExecutor.executeScript(
                "test_script_" + System.currentTimeMillis(), 
                scriptContent, 
                null // 无参数
            );
            
            if (executionId != null) {
                Log.d(TAG, "脚本执行已启动，执行ID: " + executionId);
                showToast("脚本执行已启动");
                
                // 等待脚本执行完成，最多等待10秒
                // 由于AutoJsExecutor没有提供直接的回调机制来通知测试用例，
                // 我们采用轮询的方式来检查执行状态
                long startTime = System.currentTimeMillis();
                while ((System.currentTimeMillis() - startTime) < 10000) {
                    // 检查是否有执行统计信息表明脚本已完成
                    java.util.Map<String, Object> stats = autoJsExecutor.getExecutionStatistics();
                    Integer total = (Integer) stats.get("totalExecutions");
                    Integer successful = (Integer) stats.get("successfulExecutions");
                    Integer failed = (Integer) stats.get("failedExecutions");
                    
                    // 如果总执行数大于0，且成功或失败数也大于0，说明执行已完成
                    if (total != null && total > 0) {
                        if ((successful != null && successful > 0) || (failed != null && failed > 0)) {
                            scriptExecutionCompleted.set(true);
                            scriptExecutionSuccess.set(successful != null && successful > 0);
                            break;
                        }
                    }

                    Thread.sleep(500);
                }

                if (scriptExecutionCompleted.get()) {
                    Log.d(TAG, "脚本执行完成，结果: " + (scriptExecutionSuccess.get() ? "成功" : "失败"));
                    showToast("脚本执行完成: " + (scriptExecutionSuccess.get() ? "成功" : "失败"));
                    return true;
                } else {
                    Log.w(TAG, "脚本执行超时");
                    showToast("脚本执行超时");
                    return false;
                }
            } else {
                Log.e(TAG, "脚本执行启动失败");
                showToast("脚本执行启动失败");
                return false;
            }
        } catch (Exception e) {
            Log.e(TAG, "执行脚本时出错: " + e.getMessage(), e);
            showToast("执行脚本时出错: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 反复点击导航栏标签
     */
    private void clickNavigationTabsRepeatedly() throws InterruptedException {
        Log.d(TAG, "开始反复点击导航栏标签");
        showToast("开始反复点击导航栏标签");
        
        // 启动MainActivity
        ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class);
        
        // 等待应用加载
        Thread.sleep(3000);
        
        // 反复点击导航栏20次
        for (int i = 0; i < 20; i++) {
            Log.d(TAG, "第 " + (i + 1) + " 次点击导航栏");
            showToast("第 " + (i + 1) + " 次点击导航栏");
            
            // 按顺序点击HOME, SETTINGS, DEBUG标签
            int tabPosition = i % 3;
            String tabName = getTabName(tabPosition);
            
            clickTabByPosition(scenario, tabPosition, tabName);
            
            // 每次点击之间间隔1秒
            Thread.sleep(1000);
        }
        
        Log.d(TAG, "导航栏点击测试完成");
        showToast("导航栏点击测试完成");
    }
    
    /**
     * 根据位置获取标签名称
     */
    private String getTabName(int position) {
        switch (position) {
            case 0: return "HOME";
            case 1: return "SETTINGS";
            case 2: return "DEBUG";
            default: return "UNKNOWN";
        }
    }
    
    /**
     * 点击指定位置的标签
     */
    private void clickTabByPosition(ActivityScenario<MainActivity> scenario, int position, String tabName) throws InterruptedException {
        CountDownLatch latch = new CountDownLatch(1);
        new Handler(Looper.getMainLooper()).post(() -> {
            try {
                scenario.onActivity(activity -> {
                    TabLayout tabLayout = activity.findViewById(R.id.tab_layout);
                    TabLayout.Tab tab = tabLayout.getTabAt(position);
                    if (tab != null) {
                        tab.select();
                        Log.d(TAG, "已选择 " + tabName + " 标签");
                    } else {
                        Log.w(TAG, "未找到 " + tabName + " 标签，位置: " + position);
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "选择标签时出错: " + e.getMessage());
            } finally {
                latch.countDown();
            }
        });
        
        // 等待标签选择完成
        try {
            latch.await(5, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Log.e(TAG, "等待标签选择时被中断");
            throw e;
        }
    }
    
    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        new Handler(Looper.getMainLooper()).post(() -> {
            Toast.makeText(context, message, Toast.LENGTH_SHORT).show();
        });
    }
    
    @After
    public void tearDown() {
        try {
            if (scriptDownloader != null) {
                scriptDownloader.destroy();
            }
        } catch (Exception e) {
            Log.w(TAG, "销毁资源时出错: " + e.getMessage());
        }
    }
}