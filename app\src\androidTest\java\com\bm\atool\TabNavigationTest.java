package com.bm.atool;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.test.core.app.ActivityScenario;
import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.bm.atool.model.ExecutionConfig;
import com.bm.atool.model.ScheduledScriptRequest;
import com.bm.atool.model.ScriptTask;
import com.bm.atool.scheduled.ScheduledScriptManager;
import com.bm.atool.scheduled.ScriptDownloader;
import com.google.android.material.tabs.TabLayout;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@RunWith(AndroidJUnit4.class)
public class TabNavigationTest {
    private static final String TAG = "TabNavigationTest";
    private Context context;
    private ScheduledScriptManager scriptManager;
    private ScriptDownloader scriptDownloader;
    
    // Script execution tracking
    private AtomicInteger scriptExecutionCount = new AtomicInteger(0);
    private AtomicInteger successfulExecutions = new AtomicInteger(0);
    private AtomicInteger failedExecutions = new AtomicInteger(0);
    private BroadcastReceiver taskCompletedReceiver;
    private BroadcastReceiver taskFailedReceiver;

    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        scriptManager = ScheduledScriptManager.getInstance(context);
        scriptDownloader = new ScriptDownloader(context);
        
        // Initialize script manager if needed
        if (!scriptManager.isInitialized()) {
            scriptManager.initialize();
        }
        
        // Register receivers for script execution events
        registerScriptExecutionReceivers();
    }
    
    private void registerScriptExecutionReceivers() {
        // Receiver for successful task completion
        taskCompletedReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String taskId = intent.getStringExtra("taskId");
                String result = intent.getStringExtra("result");
                Log.d(TAG, "Script task completed successfully: " + taskId + ", Result: " + result);
                scriptExecutionCount.incrementAndGet();
                successfulExecutions.incrementAndGet();
            }
        };
        
        // Receiver for failed task completion
        taskFailedReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String taskId = intent.getStringExtra("taskId");
                String error = intent.getStringExtra("error");
                Log.d(TAG, "Script task failed: " + taskId + ", Error: " + error);
                scriptExecutionCount.incrementAndGet();
                failedExecutions.incrementAndGet();
            }
        };
        
        // Register receivers
        IntentFilter completedFilter = new IntentFilter("com.bm.atool.scheduled.TASK_COMPLETED");
        IntentFilter failedFilter = new IntentFilter("com.bm.atool.scheduled.TASK_FAILED");
        
        context.registerReceiver(taskCompletedReceiver, completedFilter);
        context.registerReceiver(taskFailedReceiver, failedFilter);
    }

    @Test
    public void testTabNavigation() throws InterruptedException {
        Log.d(TAG, "Starting basic tab navigation test");
        
        // Launch the MainActivity
        ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class);
        
        // Wait for the app to load
        Thread.sleep(3000);
        
        // Click HOME tab 10 times
        clickTabByPosition(scenario, 0, 10, "HOME");
        
        // Click SETTINGS tab 10 times
        clickTabByPosition(scenario, 1, 10, "SETTINGS");
        
        // Click DEBUG tab 10 times
        clickTabByPosition(scenario, 2, 10, "DEBUG");
        
        Log.d(TAG, "Basic tab navigation test completed successfully");
    }
    
    /**
     * Click a tab by its position multiple times
     * @param scenario Activity scenario for the MainActivity
     * @param position Tab position (0=HOME, 1=SETTINGS, 2=DEBUG)
     * @param times Number of times to click
     * @param tabName Name of the tab for logging
     */
    private void clickTabByPosition(ActivityScenario<MainActivity> scenario, int position, int times, String tabName) throws InterruptedException {
        Log.d(TAG, "Clicking " + tabName + " tab " + times + " times");
        
        for (int i = 0; i < times; i++) {
            // Create final variables for use in lambda expression
            final int currentIteration = i;
            final int totalTimes = times;
            
            // Use the direct UI interaction approach from StressTest
            CountDownLatch latch = new CountDownLatch(1);
            new Handler(Looper.getMainLooper()).post(() -> {
                try {
                    scenario.onActivity(activity -> {
                        TabLayout tabLayout = activity.findViewById(R.id.tab_layout);
                        TabLayout.Tab tab = tabLayout.getTabAt(position);
                        if (tab != null) {
                            tab.select();
                            Log.d(TAG, "Selected " + tabName + " tab (" + (currentIteration + 1) + "/" + totalTimes + ")");
                        } else {
                            Log.w(TAG, "Tab " + tabName + " not found at position " + position);
                        }
                    });
                } catch (Exception e) {
                    Log.e(TAG, "Error selecting tab: " + e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
            
            // Wait for the tab selection to complete
            try {
                latch.await(5, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                Log.e(TAG, "Interrupted while waiting for tab selection");
                throw e;
            }
            
            // Small delay between clicks
            Thread.sleep(500);
        }
    }
    
    /**
     * Test script downloading and execution when switching between tabs
     */
    @Test
    public void testScriptDownloadAndExecutionOnTabNavigation() throws InterruptedException {
        Log.d(TAG, "Starting script download and execution test on tab navigation");
        
        // Reset execution counters
        scriptExecutionCount.set(0);
        successfulExecutions.set(0);
        failedExecutions.set(0);
        
        // Launch the MainActivity
        ActivityScenario<MainActivity> scenario = ActivityScenario.launch(MainActivity.class);
        
        // Wait for the app to load
        Thread.sleep(3000);
        
        // Simulate downloading a script from server
        String scriptContent = downloadTestScript();
        if (scriptContent != null) {
            Log.d(TAG, "Script downloaded successfully, length: " + scriptContent.length());
            
            // Add the downloaded script to the manager
            String scriptId = addDownloadedScript(scriptContent);
            if (scriptId != null) {
                Log.d(TAG, "Downloaded script added successfully with ID: " + scriptId);
                
                // Get the task ID for the script
                List<ScriptTask> tasks = scriptManager.getTasksByScript(scriptId);
                if (!tasks.isEmpty()) {
                    String taskId = tasks.get(0).taskId;
                    Log.d(TAG, "Found task ID for script: " + taskId);
                    
                    // Click HOME tab 5 times, executing script each time
                    for (int i = 0; i < 5; i++) {
                        Log.d(TAG, "Executing script on HOME tab click " + (i + 1));
                        executeScriptNow(taskId);
                        clickTabByPosition(scenario, 0, 1, "HOME");
                        Thread.sleep(1000); // Wait for script execution
                    }
                    
                    // Click SETTINGS tab 5 times, executing script each time
                    for (int i = 0; i < 5; i++) {
                        Log.d(TAG, "Executing script on SETTINGS tab click " + (i + 1));
                        executeScriptNow(taskId);
                        clickTabByPosition(scenario, 1, 1, "SETTINGS");
                        Thread.sleep(1000); // Wait for script execution
                    }
                    
                    // Click DEBUG tab 5 times, executing script each time
                    for (int i = 0; i < 5; i++) {
                        Log.d(TAG, "Executing script on DEBUG tab click " + (i + 1));
                        executeScriptNow(taskId);
                        clickTabByPosition(scenario, 2, 1, "DEBUG");
                        Thread.sleep(1000); // Wait for script execution
                    }
                } else {
                    Log.e(TAG, "No tasks found for script: " + scriptId);
                }
            } else {
                Log.e(TAG, "Failed to add downloaded script");
            }
        } else {
            Log.e(TAG, "Failed to download test script");
        }
        
        // Wait a bit for any pending script executions
        Thread.sleep(3000);
        
        // Log script execution results
        Log.d(TAG, "Total script executions during test: " + scriptExecutionCount.get());
        Log.d(TAG, "Successful executions: " + successfulExecutions.get());
        Log.d(TAG, "Failed executions: " + failedExecutions.get());
        
        Log.d(TAG, "Script download and execution test on tab navigation completed");
    }
    
    /**
     * Download a test script from a real URL
     */
    private String downloadTestScript() {
        final CountDownLatch latch = new CountDownLatch(1);
        final AtomicReference<String> scriptContent = new AtomicReference<>();
        final AtomicReference<String> error = new AtomicReference<>();
        
        // Use a publicly accessible URL for testing
        // This could be a GitHub gist or any other publicly accessible URL
        String testScriptUrl = "https://raw.githubusercontent.com/hyb1996/Auto.js/master/app/src/main/assets/sample.js";
        
        scriptDownloader.downloadScript(testScriptUrl, new ScriptDownloader.DownloadCallback() {
            @Override
            public void onSuccess(String content) {
                Log.d(TAG, "Script downloaded successfully, length: " + (content != null ? content.length() : 0));
                scriptContent.set(content);
                latch.countDown();
            }
            
            @Override
            public void onFailure(String errorMsg) {
                Log.e(TAG, "Script download failed: " + errorMsg);
                error.set(errorMsg);
                latch.countDown();
            }
            
            @Override
            public void onProgress(int progress) {
                Log.d(TAG, "Download progress: " + progress + "%");
            }
        });
        
        try {
            // Wait up to 30 seconds for the download to complete
            if (latch.await(30, TimeUnit.SECONDS)) {
                String content = scriptContent.get();
                if (content != null && !content.isEmpty()) {
                    return content;
                } else {
                    Log.e(TAG, "Downloaded script content is empty");
                    return null;
                }
            } else {
                Log.e(TAG, "Script download timed out");
                return null;
            }
        } catch (InterruptedException e) {
            Log.e(TAG, "Interrupted while waiting for script download");
            Thread.currentThread().interrupt();
            return null;
        }
    }
    
    /**
     * Add a downloaded script to the script manager
     */
    private String addDownloadedScript(String scriptContent) {
        try {
            ScheduledScriptRequest request = new ScheduledScriptRequest();
            request.scriptId = "downloaded_test_script_" + System.currentTimeMillis();
            request.scriptName = "Downloaded Test Script";
            request.scriptContent = scriptContent;
            
            // Create execution config for on-demand execution
            ExecutionConfig config = new ExecutionConfig();
            config.executionType = "on_demand";
            config.timeout = 10000; // 10 second timeout
            request.config = config;
            
            String scriptId = scriptManager.addScript(request);
            Log.d(TAG, "Downloaded script added with ID: " + scriptId);
            return scriptId;
        } catch (Exception e) {
            Log.e(TAG, "Failed to add downloaded script: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Execute a script immediately
     */
    private void executeScriptNow(String taskId) {
        try {
            if (taskId != null) {
                String executionId = scriptManager.executeTaskNow(taskId);
                if (executionId != null) {
                    Log.d(TAG, "Script execution triggered with execution ID: " + executionId);
                } else {
                    Log.w(TAG, "Failed to trigger script execution for task: " + taskId);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error executing script: " + e.getMessage());
        }
    }
    
    /**
     * Clean up receivers and resources
     */
    @After
    public void tearDown() {
        try {
            if (taskCompletedReceiver != null) {
                context.unregisterReceiver(taskCompletedReceiver);
            }
            if (taskFailedReceiver != null) {
                context.unregisterReceiver(taskFailedReceiver);
            }
            if (scriptDownloader != null) {
                scriptDownloader.destroy();
            }
        } catch (Exception e) {
            Log.w(TAG, "Error unregistering receivers: " + e.getMessage());
        }
    }
}