package com.stardust.autojs.engine;

import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import java.util.HashMap;
import java.util.Map;

/**
 * ScriptEngine单元测试
 * 测试JavaScript脚本生成的正确性
 */
public class ScriptEngineTest {
    
    private TestableScriptEngine scriptEngine;
    
    @Before
    public void setUp() {
        scriptEngine = new TestableScriptEngine();
    }
    
    @Test
    public void testBuildScriptWithEmptyParams() {
        String userScript = "console.log('Hello World');";
        String result = scriptEngine.testBuildScript(userScript);
        
        assertNotNull("生成的脚本不应为null", result);
        assertTrue("脚本应包含用户代码", result.contains("Hello World"));
        assertTrue("脚本应包含execArgv定义", result.contains("const execArgv = {};"));
        assertTrue("脚本应以javascript:开头", result.startsWith("javascript:"));
        assertTrue("脚本应以})();结尾", result.endsWith("})();"));
        
        // 验证device对象语法正确
        assertTrue("脚本应包含正确的device对象", result.contains("const device = {"));
        assertTrue("device对象应包含width属性", result.contains("width: 1440,"));
        assertTrue("device对象应包含height属性", result.contains("height: 3120"));
        assertTrue("device对象应正确闭合", result.contains("};"));
    }
    
    @Test
    public void testBuildScriptWithParams() {
        Map<String, Object> params = new HashMap<>();
        params.put("testString", "hello");
        params.put("testNumber", 42);
        params.put("testBoolean", true);

        scriptEngine.setParams(params);
        String userScript = "console.log('Test with params');";
        String result = scriptEngine.testBuildScript(userScript);

        System.out.println("Generated script: " + result);

        assertNotNull("生成的脚本不应为null", result);
        assertTrue("脚本应包含字符串参数", result.contains("'testString':'hello'"));
        assertTrue("脚本应包含数字参数", result.contains("'testNumber':42"));
        assertTrue("脚本应包含布尔参数", result.contains("'testBoolean':true"));

        // 验证参数对象语法正确（不应有多余的逗号）
        assertFalse("JavaScript对象不应有多余的逗号", result.contains(",}"));
    }

    @Test
    public void testSimpleHelloScript() {
        // 测试简单的Hello World脚本
        String userScript = "console.log('Hello World!');\n" +
                "toast('Hello World!');";

        String result = scriptEngine.testBuildScript(userScript);

        assertNotNull("生成的脚本不应为null", result);
        assertTrue("脚本应包含用户代码", result.contains("Hello World!"));
        assertTrue("脚本应以javascript:开头", result.startsWith("javascript:"));
        assertTrue("脚本应以})();结尾", result.endsWith("})();"));

        // 验证生成的JavaScript语法
        assertTrue("脚本应包含正确的函数定义", result.contains("function toast(msg)"));
        assertTrue("脚本应包含console对象", result.contains("console = {"));

        // 验证没有语法错误的迹象
        assertFalse("JavaScript对象不应有多余的逗号", result.contains(",}"));
        assertFalse("不应有未闭合的括号", result.contains("}{"));
        assertFalse("不应有多余的分号", result.contains(";;"));

        System.out.println("生成的简单脚本: " + result);
    }
    
    @Test
    public void testBuildScriptWithComplexUserScript() {
        String userScript = "/**\n" +
                " * 模拟脚本：5秒后点击导航栏20次\n" +
                " */\n\n" +
                "auto.waitFor();\n\n" +
                "toast(\"开始执行模拟点击脚本\");\n" +
                "console.log(\"开始执行模拟点击脚本\");\n\n" +
                "console.log(\"等待5秒...\");\n" +
                "sleep(5000);\n\n" +
                "toast(\"5秒已到，开始点击导航栏\");\n" +
                "console.log(\"5秒已到，开始点击导航栏\");\n\n" +
                "for (var i = 0; i < 20; i++) {\n" +
                "    var x = device.width / 2;\n" +
                "    var y = device.height - 50;\n" +
                "    var clickResult = click(x, y);\n" +
                "    console.log(\"第\" + (i + 1) + \"次点击导航栏，坐标: (\" + x + \", \" + y + \"), 结果: \" + clickResult);\n" +
                "    if ((i + 1) % 5 == 0) {\n" +
                "        toast(\"已完成\" + (i + 1) + \"次点击\");\n" +
                "    }\n" +
                "    sleep(100);\n" +
                "}\n\n" +
                "toast(\"模拟点击脚本执行完成\");\n" +
                "console.log(\"模拟点击脚本执行完成\");";
        
        String result = scriptEngine.testBuildScript(userScript);
        
        assertNotNull("生成的脚本不应为null", result);
        assertTrue("脚本应包含用户代码", result.contains("模拟点击脚本执行完成"));
        
        // 验证生成的JavaScript语法
        assertTrue("脚本应包含正确的函数定义", result.contains("function click(x, y)"));
        assertTrue("脚本应包含正确的函数定义", result.contains("function sleep(ms)"));
        assertTrue("脚本应包含正确的函数定义", result.contains("function toast(msg)"));
        
        // 验证没有语法错误的迹象
        assertFalse("不应有未闭合的括号", result.contains("}{"));
        assertFalse("不应有多余的分号", result.contains(";;"));
    }
    
    @Test
    public void testJsonStringEscaping() {
        String userScript = "console.log(\"Hello \\\"World\\\"\");";
        String result = scriptEngine.testBuildScript(userScript);
        
        assertNotNull("生成的脚本不应为null", result);
        // 验证字符串转义正确
        assertTrue("应正确转义引号", result.contains("\\\""));
    }
    
    /**
     * 可测试的ScriptEngine子类，暴露内部方法用于测试
     */
    private static class TestableScriptEngine {
        private Map<String, Object> params = new HashMap<>();
        
        public void setParams(Map<String, Object> params) {
            this.params = params;
        }
        
        public String testBuildScript(String userScript) {
            return buildScript(userScript);
        }
        
        private String buildScript(String userScript) {
            // 检查脚本是否为空
            if (userScript == null || userScript.trim().isEmpty()) {
                return "javascript:(function() { console.error('脚本内容为空'); })();";
            }

            // 构建参数传递
            StringBuilder paramsScript = new StringBuilder();
            paramsScript.append("const execArgv = {");
            boolean first = true;
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                if (!first) {
                    paramsScript.append(",");
                }
                paramsScript.append("'").append(entry.getKey()).append("':");
                if (entry.getValue() instanceof String) {
                    paramsScript.append("'").append(entry.getValue()).append("'");
                } else if (entry.getValue() instanceof Number || entry.getValue() instanceof Boolean) {
                    paramsScript.append(entry.getValue());
                } else {
                    paramsScript.append("null");
                }
                first = false;
            }
            paramsScript.append("};");
            
            // 转义用户脚本中的特殊字符
            String escapedUserScript = escapeScript(userScript);
            
            // 构建完整脚本
            return "javascript:(function() {" +
                    "let stopExecution = false;" +
                    
                    // 重写console函数
                    "console = {" +
                    "  log: function(msg) { android.console('LOG', msg); }," +
                    "  info: function(msg) { android.console('INFO', msg); }," +
                    "  warn: function(msg) { android.console('WARN', msg); }," +
                    "  error: function(msg) { android.console('ERROR', msg); }" +
                    "};" +
                    
                    // 重写Toast函数
                    "function toast(msg) { android.showToast(msg); };" +
                    
                    // 模拟auto.waitFor()
                    "const auto = {" +
                    "  waitFor: function() { console.log('请求无障碍权限'); return true; }" +
                    "};" +
                    
                    // 设备信息
                    "const device = {" +
                    "  width: " + getScreenWidth() + "," +
                    "  height: " + getScreenHeight() +
                    "};" +
                    
                    // 模拟点击函数
                    "function click(x, y) {" +
                    "  if(stopExecution) return false;" +
                    "  console.log('点击: ' + x + ', ' + y);" +
                    "  try {" +
                    "    const result = android.click(x, y);" +
                    "    console.log('点击结果: ' + result);" +
                    "    return result;" +
                    "  } catch (e) {" +
                    "    console.error('点击异常: ' + e);" +
                    "    return false;" +
                    "  }" +
                    "};" +
                    
                    // 滑动函数
                    "function swipe(x1, y1, x2, y2, duration) {" +
                    "  if(stopExecution) return false;" +
                    "  duration = duration || 500;" +
                    "  console.log('滑动: (' + x1 + ', ' + y1 + ') -> (' + x2 + ', ' + y2 + '), 持续: ' + duration + 'ms');" +
                    "  return android.swipe(x1, y1, x2, y2, duration);" +
                    "};" +
                    
                    // 长按函数
                    "function longClick(x, y, duration) {" +
                    "  if(stopExecution) return false;" +
                    "  duration = duration || 800;" +
                    "  console.log('长按: (' + x + ', ' + y + '), 持续: ' + duration + 'ms');" +
                    "  return android.longClick(x, y, duration);" +
                    "};" +
                    
                    // 睡眠函数
                    "function sleep(ms) {" +
                    "  if(stopExecution) return;" +
                    "  const start = new Date().getTime();" +
                    "  while(new Date().getTime() < start + ms) {" +
                    "    if((new Date().getTime() - start) % 1000 === 0) {" +
                    "      console.log('sleep: ' + (new Date().getTime() - start) + 'ms');" +
                    "    }" +
                    "    if(stopExecution) return;" +
                    "  }" +
                    "};" +
                    
                    // 参数
                    paramsScript.toString() +
                    
                    // 用户脚本
                    "try { eval(" + toJsonString(escapedUserScript) + "); } catch(e) { console.error('脚本执行错误: ' + e); }" +
                    "})();";
        }
        
        private String escapeScript(String script) {
            if (script == null || script.isEmpty()) {
                return "";
            }
            return script;
        }
        
        private String toJsonString(String str) {
            if (str == null) {
                return "null";
            }
            
            StringBuilder sb = new StringBuilder();
            sb.append("\"");
            
            for (int i = 0; i < str.length(); i++) {
                char c = str.charAt(i);
                switch (c) {
                    case '"':
                        sb.append("\\\"");
                        break;
                    case '\\':
                        sb.append("\\\\");
                        break;
                    case '\b':
                        sb.append("\\b");
                        break;
                    case '\f':
                        sb.append("\\f");
                        break;
                    case '\n':
                        sb.append("\\n");
                        break;
                    case '\r':
                        sb.append("\\r");
                        break;
                    case '\t':
                        sb.append("\\t");
                        break;
                    default:
                        if (c < 0x20) {
                            sb.append(String.format("\\u%04x", (int) c));
                        } else {
                            sb.append(c);
                        }
                        break;
                }
            }
            
            sb.append("\"");
            return sb.toString();
        }
        
        private int getScreenWidth() {
            return 1440; // 模拟屏幕宽度
        }
        
        private int getScreenHeight() {
            return 3120; // 模拟屏幕高度
        }
    }
}
