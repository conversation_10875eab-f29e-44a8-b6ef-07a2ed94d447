package com.bm.atool.utils;

import android.accessibilityservice.AccessibilityService;
import android.accessibilityservice.GestureDescription;
import android.graphics.Path;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.accessibility.AccessibilityNodeInfo;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;

import com.bm.atool.service.ANTAccessibilityService;

/**
 * 无障碍操作工具类
 * 提供点击、滑动、长按等操作
 */
public class AccessibilityUtil {
    private static final String TAG = "AccessibilityUtil";
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * 点击屏幕坐标
     *
     * @param x X坐标
     * @param y Y坐标
     * @return 是否点击成功
     */
    public static boolean click(int x, int y) {
        Log.d(TAG, "尝试点击坐标: (" + x + ", " + y + ")");
        
        // 使用ANTAccessibilityService服务
        AccessibilityService service = ANTAccessibilityService.getInstance();
        if (service != null) {
            return performClick(service, x, y);
        }
        
        Log.e(TAG, "无法执行点击操作：无障碍服务未运行");
        return false;
    }

    /**
     * 在指定服务上执行点击操作
     */
    private static boolean performClick(AccessibilityService service, int x, int y) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            // 使用Android N以上的手势API
            return performGestureClick(service, x, y);
        } else {
            // 尝试使用节点查找方式点击
            return performLegacyClick(service, x, y);
        }
    }

    /**
     * 使用手势API执行点击（Android N及以上）
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    private static boolean performGestureClick(AccessibilityService service, int x, int y) {
        Path path = new Path();
        path.moveTo(x, y);
        
        GestureDescription.Builder builder = new GestureDescription.Builder();
        builder.addStroke(new GestureDescription.StrokeDescription(path, 0, 50));
        GestureDescription gesture = builder.build();
        
        final boolean[] result = {false};
        final Object lock = new Object();
        
        service.dispatchGesture(gesture, new AccessibilityService.GestureResultCallback() {
            @Override
            public void onCompleted(GestureDescription gestureDescription) {
                synchronized (lock) {
                    result[0] = true;
                    lock.notify();
                }
                Log.d(TAG, "点击手势完成: (" + x + ", " + y + ")");
            }

            @Override
            public void onCancelled(GestureDescription gestureDescription) {
                synchronized (lock) {
                    result[0] = false;
                    lock.notify();
                }
                Log.e(TAG, "点击手势被取消: (" + x + ", " + y + ")");
            }
        }, null);
        
        // 等待手势完成或超时
        synchronized (lock) {
            try {
                lock.wait(1000); // 最多等待1秒
            } catch (InterruptedException e) {
                Log.e(TAG, "点击手势等待被中断", e);
            }
        }
        
        return result[0];
    }

    /**
     * 使用节点查找方式点击（Android N以下版本）
     */
    private static boolean performLegacyClick(AccessibilityService service, int x, int y) {
        try {
            // 获取根节点
            AccessibilityNodeInfo rootNode = service.getRootInActiveWindow();
            if (rootNode == null) {
                Log.e(TAG, "无法获取活动窗口的根节点");
                return false;
            }
            
            // 尝试使用触摸方式点击
            return rootNode.performAction(AccessibilityNodeInfo.ACTION_CLICK);
        } catch (Exception e) {
            Log.e(TAG, "执行传统点击时发生异常: " + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 滑动操作
     *
     * @param startX 起始X坐标
     * @param startY 起始Y坐标
     * @param endX 结束X坐标
     * @param endY 结束Y坐标
     * @param duration 滑动持续时间（毫秒）
     * @return 是否成功执行滑动
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static boolean swipe(int startX, int startY, int endX, int endY, long duration) {
        Log.d(TAG, "执行滑动: (" + startX + ", " + startY + ") -> (" + endX + ", " + endY + "), 持续: " + duration + "ms");
        
        // 使用ANTAccessibilityService服务
        AccessibilityService service = ANTAccessibilityService.getInstance();
        
        if (service == null) {
            Log.e(TAG, "无法执行滑动操作：无障碍服务未运行");
            return false;
        }
        
        // 创建路径
        Path path = new Path();
        path.moveTo(startX, startY);
        path.lineTo(endX, endY);
        
        // 创建手势
        GestureDescription.Builder builder = new GestureDescription.Builder();
        builder.addStroke(new GestureDescription.StrokeDescription(path, 0, duration));
        GestureDescription gesture = builder.build();
        
        // 执行手势
        final boolean[] result = {false};
        final Object lock = new Object();
        
        service.dispatchGesture(gesture, new AccessibilityService.GestureResultCallback() {
            @Override
            public void onCompleted(GestureDescription gestureDescription) {
                synchronized (lock) {
                    result[0] = true;
                    lock.notify();
                }
                Log.d(TAG, "滑动手势完成");
            }

            @Override
            public void onCancelled(GestureDescription gestureDescription) {
                synchronized (lock) {
                    result[0] = false;
                    lock.notify();
                }
                Log.e(TAG, "滑动手势被取消");
            }
        }, null);
        
        // 等待手势完成或超时
        synchronized (lock) {
            try {
                lock.wait(duration + 500); // 等待滑动持续时间+500ms
            } catch (InterruptedException e) {
                Log.e(TAG, "滑动手势等待被中断", e);
            }
        }
        
        return result[0];
    }

    /**
     * 长按操作
     *
     * @param x X坐标
     * @param y Y坐标
     * @param duration 按住持续时间（毫秒）
     * @return 是否成功执行长按
     */
    @RequiresApi(api = Build.VERSION_CODES.N)
    public static boolean longClick(int x, int y, long duration) {
        Log.d(TAG, "执行长按: (" + x + ", " + y + "), 持续: " + duration + "ms");
        
        // 使用ANTAccessibilityService服务
        AccessibilityService service = ANTAccessibilityService.getInstance();
        
        if (service == null) {
            Log.e(TAG, "无法执行长按操作：无障碍服务未运行");
            return false;
        }
        
        // 创建路径
        Path path = new Path();
        path.moveTo(x, y);
        
        // 创建手势
        GestureDescription.Builder builder = new GestureDescription.Builder();
        builder.addStroke(new GestureDescription.StrokeDescription(path, 0, duration));
        GestureDescription gesture = builder.build();
        
        // 执行手势
        final boolean[] result = {false};
        final Object lock = new Object();
        
        service.dispatchGesture(gesture, new AccessibilityService.GestureResultCallback() {
            @Override
            public void onCompleted(GestureDescription gestureDescription) {
                synchronized (lock) {
                    result[0] = true;
                    lock.notify();
                }
                Log.d(TAG, "长按手势完成");
            }

            @Override
            public void onCancelled(GestureDescription gestureDescription) {
                synchronized (lock) {
                    result[0] = false;
                    lock.notify();
                }
                Log.e(TAG, "长按手势被取消");
            }
        }, null);
        
        // 等待手势完成或超时
        synchronized (lock) {
            try {
                lock.wait(duration + 500); // 等待长按持续时间+500ms
            } catch (InterruptedException e) {
                Log.e(TAG, "长按手势等待被中断", e);
            }
        }
        
        return result[0];
    }

    /**
     * 检查无障碍服务是否可用
     */
    public static boolean isAccessibilityServiceEnabled() {
        return ANTAccessibilityService.getInstance() != null;
    }
} 