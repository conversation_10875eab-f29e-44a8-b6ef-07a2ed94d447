package com.bm.atool;

import android.content.Context;
import android.util.Log;

import androidx.test.core.app.ApplicationProvider;
import androidx.test.ext.junit.runners.AndroidJUnit4;

import com.bm.atool.utils.AutoJsExecutor;
import com.bm.atool.utils.ScriptExecutionUtil;

import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;

/**
 * 脚本执行功能测试
 */
@RunWith(AndroidJUnit4.class)
public class ScriptExecutionTest {
    private static final String TAG = "ScriptExecutionTest";

    @Test
    public void testAutoJsExecutorInitialization() {
        Context context = ApplicationProvider.getApplicationContext();
        AutoJsExecutor executor = AutoJsExecutor.getInstance(context);
        assertNotNull("AutoJsExecutor should be initialized", executor);
    }

    @Test
    public void testSimpleScriptExecution() throws InterruptedException {
        Context context = ApplicationProvider.getApplicationContext();
        AutoJsExecutor executor = AutoJsExecutor.getInstance(context);

        // 简单的脚本测试
        String script = "console.log('Hello, World!');";
        CountDownLatch latch = new CountDownLatch(1);
        
        Map<String, Object> resultContainer = new HashMap<>();
        
        String executionId = executor.executeScript("test_script", script, null, new com.stardust.autojs.execution.ScriptExecutionListener() {
            @Override
            public void onStart(com.stardust.autojs.execution.ScriptExecution execution) {
                Log.d(TAG, "Script execution started: " + execution.getId());
            }

            @Override
            public void onSuccess(com.stardust.autojs.execution.ScriptExecution execution, Object result) {
                Log.d(TAG, "Script execution successful: " + result);
                resultContainer.put("result", result);
                resultContainer.put("success", true);
                latch.countDown();
            }

            @Override
            public void onException(com.stardust.autojs.execution.ScriptExecution execution, Throwable e) {
                Log.e(TAG, "Script execution failed", e);
                resultContainer.put("error", e.getMessage());
                resultContainer.put("success", false);
                latch.countDown();
            }
        });
        
        assertNotNull("Execution ID should not be null", executionId);
        
        // 等待执行完成
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assertTrue("Script execution should complete within 10 seconds", completed);
        
        Boolean success = (Boolean) resultContainer.get("success");
        assertTrue("Script execution should be successful", success != null && success);
    }

    @Test
    public void testScriptWithParams() throws InterruptedException {
        Context context = ApplicationProvider.getApplicationContext();
        AutoJsExecutor executor = AutoJsExecutor.getInstance(context);
        
        // 带参数的脚本测试
        String script = "console.log('Name: ' + execArgv.name + ', Age: ' + execArgv.age);";
        Map<String, Object> params = new HashMap<>();
        params.put("name", "ATool");
        params.put("age", 1);
        
        CountDownLatch latch = new CountDownLatch(1);
        Map<String, Object> resultContainer = new HashMap<>();
        
        String executionId = executor.executeScript("test_script_params", script, params, new com.stardust.autojs.execution.ScriptExecutionListener() {
            @Override
            public void onStart(com.stardust.autojs.execution.ScriptExecution execution) {
                Log.d(TAG, "Script execution started: " + execution.getId());
            }

            @Override
            public void onSuccess(com.stardust.autojs.execution.ScriptExecution execution, Object result) {
                Log.d(TAG, "Script execution successful: " + result);
                resultContainer.put("result", result);
                resultContainer.put("success", true);
                latch.countDown();
            }

            @Override
            public void onException(com.stardust.autojs.execution.ScriptExecution execution, Throwable e) {
                Log.e(TAG, "Script execution failed", e);
                resultContainer.put("error", e.getMessage());
                resultContainer.put("success", false);
                latch.countDown();
            }
        });
        
        assertNotNull("Execution ID should not be null", executionId);
        
        // 等待执行完成
        boolean completed = latch.await(10, TimeUnit.SECONDS);
        assertTrue("Script execution should complete within 10 seconds", completed);
        
        Boolean success = (Boolean) resultContainer.get("success");
        assertTrue("Script execution should be successful", success != null && success);
    }

    @Test
    public void testScriptExecutionUtil() {
        Context context = ApplicationProvider.getApplicationContext();
        // 这个测试主要验证工具类能正常调用，但不会实际执行脚本（因为无障碍服务未运行）
        boolean result = ScriptExecutionUtil.executeSimulatedScript(context);
        // 在测试环境中，无障碍服务未运行，所以应该返回false
        Log.d(TAG, "ScriptExecutionUtil result: " + result);
    }
    
    @Test
    public void testComplexScriptExecution() throws InterruptedException {
        Context context = ApplicationProvider.getApplicationContext();
        AutoJsExecutor executor = AutoJsExecutor.getInstance(context);
        
        // 复杂脚本测试，包含特殊字符和多行代码
        String script =
            "console.log('开始执行复杂脚本');\n" +
            "var message = 'Hello, 世界!';\n" +
            "console.log(message);\n" +
            "toast(message);\n" +
            "\n" +
            "// 测试循环\n" +
            "for (var i = 0; i < 3; i++) {\n" +
            "    console.log('循环次数: ' + (i + 1));\n" +
            "    sleep(100);\n" +
            "}\n" +
            "\n" +
            "// 测试条件语句\n" +
            "if (execArgv && execArgv.testParam) {\n" +
            "    console.log('参数值: ' + execArgv.testParam);\n" +
            "} else {\n" +
            "    console.log('未提供参数');\n" +
            "}\n" +
            "\n" +
            "console.log('复杂脚本执行完成');";
        
        Map<String, Object> params = new HashMap<>();
        params.put("testParam", "测试参数值");
        
        CountDownLatch latch = new CountDownLatch(1);
        Map<String, Object> resultContainer = new HashMap<>();
        
        String executionId = executor.executeScript("test_complex_script", script, params, new com.stardust.autojs.execution.ScriptExecutionListener() {
            @Override
            public void onStart(com.stardust.autojs.execution.ScriptExecution execution) {
                Log.d(TAG, "Complex script execution started: " + execution.getId());
            }

            @Override
            public void onSuccess(com.stardust.autojs.execution.ScriptExecution execution, Object result) {
                Log.d(TAG, "Complex script execution successful: " + result);
                resultContainer.put("result", result);
                resultContainer.put("success", true);
                latch.countDown();
            }

            @Override
            public void onException(com.stardust.autojs.execution.ScriptExecution execution, Throwable e) {
                Log.e(TAG, "Complex script execution failed", e);
                resultContainer.put("error", e.getMessage());
                resultContainer.put("success", false);
                latch.countDown();
            }
        });
        
        assertNotNull("Execution ID should not be null", executionId);
        
        // 等待执行完成
        boolean completed = latch.await(15, TimeUnit.SECONDS);
        assertTrue("Complex script execution should complete within 15 seconds", completed);
        
        Boolean success = (Boolean) resultContainer.get("success");
        assertTrue("Complex script execution should be successful", success != null && success);
    }
}