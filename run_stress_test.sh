#!/bin/bash

# 压力测试运行脚本

echo "开始运行Android压力测试..."

# 确保设备已连接
adb devices | grep -q "device$"
if [ $? -ne 0 ]; then
  echo "错误: 没有找到已连接的Android设备"
  exit 1
fi

# 清除应用数据以确保干净的测试环境
echo "清除应用数据..."
#adb shell pm clear com.bm.atool
 
# 安装应用（如果需要）
# echo "安装应用..."
# adb install -r app/build/outputs/apk/debug/app-debug.apk

# 运行测试
echo "运行压力测试..."
./gradlew connectedAndroidTest -Pandroid.testInstrumentationRunnerArguments.class=com.bm.atool.StressTest

# 获取测试日志
echo "获取测试日志..."
TEST_LOG_DIR="stress_test_logs"
mkdir -p $TEST_LOG_DIR

# 获取Android设备上的日志文件路径
LOG_PATH=$(adb shell run-as com.bm.atool find /storage/emulated/0/Android/data/com.bm.atool/files/stress_test_logs -type f -name "*.log" | sort -r | head -n 1 | tr -d '\r')

if [ -z "$LOG_PATH" ]; then
  echo "警告: 没有找到测试日志文件"
else
  # 提取日志文件名
  LOG_FILENAME=$(basename "$LOG_PATH")
  
  # 拉取最新的日志文件
  echo "拉取日志文件: $LOG_FILENAME"
  adb pull "$LOG_PATH" "$TEST_LOG_DIR/$LOG_FILENAME"
  
  # 显示日志内容
  echo "测试日志内容:"
  cat "$TEST_LOG_DIR/$LOG_FILENAME"
  
  # 分析结果
  echo "分析测试结果..."
  ONLINE_COUNT=$(grep "Socket连接状态: 在线" "$TEST_LOG_DIR/$LOG_FILENAME" | wc -l)
  OFFLINE_COUNT=$(grep "Socket连接状态: 离线" "$TEST_LOG_DIR/$LOG_FILENAME" | wc -l)
  PING_COUNT=$(grep "收到PONG心跳" "$TEST_LOG_DIR/$LOG_FILENAME" | wc -l)
  
  echo "测试结果摘要:"
  echo "- 在线状态记录次数: $ONLINE_COUNT"
  echo "- 离线状态记录次数: $OFFLINE_COUNT"
  echo "- 收到PONG心跳次数: $PING_COUNT"
  
  if [ $OFFLINE_COUNT -eq 0 ]; then
    echo "测试结果: 通过 ✓ (5分钟内没有断开连接)"
  else
    echo "测试结果: 失败 ✗ (5分钟内出现断开连接)"
  fi
fi

echo "压力测试完成" 