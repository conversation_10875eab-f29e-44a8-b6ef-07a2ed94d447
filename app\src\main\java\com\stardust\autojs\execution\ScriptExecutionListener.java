package com.stardust.autojs.execution;

/**
 * 脚本执行监听器接口
 */
public interface ScriptExecutionListener {
    /**
     * 当脚本开始执行时调用
     * @param execution 脚本执行对象
     */
    void onStart(ScriptExecution execution);

    /**
     * 当脚本执行成功时调用
     * @param execution 脚本执行对象
     * @param result 执行结果
     */
    void onSuccess(ScriptExecution execution, Object result);

    /**
     * 当脚本执行出现异常时调用
     * @param execution 脚本执行对象
     * @param e 异常
     */
    void onException(ScriptExecution execution, Throwable e);
}