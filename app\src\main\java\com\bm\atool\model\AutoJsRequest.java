package com.bm.atool.model;

import java.util.Map;

/**
 * AutoJs Request Model
 */
public class AutoJsRequest {
    /**
     * Script ID, used to identify different scripts
     */
    public String scriptId;
    
    /**
     * Action type: execute - execute script, stop - stop script, stopAll - stop all scripts
     */
    public String action;
    
    /**
     * Script content to execute
     */
    public String script;
    
    /**
     * Execution ID, used to stop specific execution
     */
    public String executionId;
    
    /**
     * Script parameters, can be accessed in script via engines.myEngine().execArgv
     */
    public Map<String, Object> params;
} 