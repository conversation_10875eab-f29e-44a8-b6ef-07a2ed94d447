# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
-verbose

# Keep AutoJs and related classes
-keep class com.stardust.autojs.** { *; }
-keep class com.bm.atool.utils.AutoJsExecutor { *; }
-keep class com.bm.atool.service.ANTAccessibilityService { *; }
-keep class com.bm.atool.utils.ScriptExecutionUtil { *; }

# Keep script engine classes
-keep class com.stardust.autojs.engine.ScriptEngine { *; }
-keep class com.stardust.autojs.engine.ScriptEngine$ScriptInterface { *; }
-keep class com.stardust.autojs.ScriptEngineService { *; }
-keep class com.stardust.autojs.execution.** { *; }
-keep class com.stardust.autojs.script.** { *; }

# Keep accessibility util classes
-keep class com.bm.atool.utils.AccessibilityUtil { *; }

# Keep socket service classes
-keep class com.bm.atool.service.SocketService { *; }

# Keep Gson annotations
-keepattributes Signature
-keepattributes *Annotation*
-keep class com.google.gson.** { *; }
-keep class com.bm.atool.model.** { *; }

# Keep JavaScript interface methods
-keepclassmembers class com.stardust.autojs.engine.ScriptEngine$ScriptInterface {
    public *;
}
