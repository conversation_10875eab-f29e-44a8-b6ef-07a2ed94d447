package com.bm.atool.model;

/**
 * 脚本执行配置
 * 定义脚本的执行策略和参数
 */
public class ExecutionConfig {
    /**
     * 执行类型：
     * - "immediate": 立即执行
     * - "scheduled": 定时执行
     * - "repeat": 重复执行
     */
    public String executionType;
    
    /**
     * 定时执行时间戳（毫秒）
     * 仅当executionType为"scheduled"或"repeat"时有效
     */
    public long scheduleTime;
    
    /**
     * 重复执行间隔（毫秒）
     * 仅当executionType为"repeat"时有效
     */
    public long repeatInterval;
    
    /**
     * 最大执行次数
     * -1表示无限制，仅对重复执行有效
     */
    public int maxExecutions;
    
    /**
     * 脚本执行超时时间（毫秒）
     * 默认30秒
     */
    public long timeout;
    
    /**
     * 是否持久化任务
     * true表示系统重启后自动恢复任务
     */
    public boolean persistent;
    
    /**
     * 执行优先级
     * 1-10，数字越大优先级越高
     */
    public int priority;
    
    /**
     * 是否允许并发执行
     * false表示如果上一次执行未完成，跳过本次执行
     */
    public boolean allowConcurrent;
    
    /**
     * 构造函数，设置默认值
     */
    public ExecutionConfig() {
        this.executionType = "immediate";
        this.timeout = 30000; // 30秒
        this.maxExecutions = 1;
        this.persistent = false;
        this.priority = 5;
        this.allowConcurrent = false;
    }
}