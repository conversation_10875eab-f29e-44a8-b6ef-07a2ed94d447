package com.bm.atool.scheduled;

import android.content.Context;
import android.util.Log;

import com.bm.atool.model.ScriptTask;
import com.bm.atool.model.TaskStatus;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 基于文件系统的脚本存储实现
 */
public class FileBasedScriptStorage implements IScriptStorage {
    private static final String TAG = ScheduledScriptConstants.TAG_SCRIPT_STORAGE;
    
    private final Context context;
    private final Gson gson;
    private File rootDir;
    private File scriptsDir;
    private File tasksDir;
    private File logsDir;
    private File tempDir;
    
    public FileBasedScriptStorage(Context context) {
        this.context = context.getApplicationContext();
        this.gson = new Gson();
    }
    
    @Override
    public boolean initialize() {
        try {
            // 创建根目录
            rootDir = new File(context.getFilesDir(), "scheduled_scripts");
            if (!rootDir.exists() && !rootDir.mkdirs()) {
                Log.e(TAG, "Failed to create root directory: " + rootDir.getAbsolutePath());
                return false;
            }
            
            // 创建子目录
            scriptsDir = new File(rootDir, ScheduledScriptConstants.SCRIPTS_DIR);
            tasksDir = new File(rootDir, ScheduledScriptConstants.TASKS_DIR);
            logsDir = new File(rootDir, ScheduledScriptConstants.LOGS_DIR);
            tempDir = new File(rootDir, ScheduledScriptConstants.TEMP_DIR);
            
            File[] dirs = {scriptsDir, tasksDir, logsDir, tempDir};
            for (File dir : dirs) {
                if (!dir.exists() && !dir.mkdirs()) {
                    Log.e(TAG, "Failed to create directory: " + dir.getAbsolutePath());
                    return false;
                }
            }
            
            Log.i(TAG, "Storage initialized successfully at: " + rootDir.getAbsolutePath());
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize storage", e);
            return false;
        }
    }    
  
  // ========== 脚本存储相关方法 ==========
    
    @Override
    public boolean saveScript(String scriptId, String content) {
        if (scriptId == null || content == null) {
            Log.e(TAG, "Script ID or content is null");
            return false;
        }
        
        try {
            File scriptFile = new File(scriptsDir, scriptId + ScheduledScriptConstants.SCRIPT_FILE_EXTENSION);
            return writeFile(scriptFile, content);
        } catch (Exception e) {
            Log.e(TAG, "Failed to save script: " + scriptId, e);
            return false;
        }
    }
    
    @Override
    public String loadScript(String scriptId) {
        if (scriptId == null) {
            return null;
        }
        
        try {
            File scriptFile = new File(scriptsDir, scriptId + ScheduledScriptConstants.SCRIPT_FILE_EXTENSION);
            return readFile(scriptFile);
        } catch (Exception e) {
            Log.e(TAG, "Failed to load script: " + scriptId, e);
            return null;
        }
    }
    
    @Override
    public boolean deleteScript(String scriptId) {
        if (scriptId == null) {
            return false;
        }
        
        try {
            File scriptFile = new File(scriptsDir, scriptId + ScheduledScriptConstants.SCRIPT_FILE_EXTENSION);
            File metaFile = new File(scriptsDir, scriptId + ScheduledScriptConstants.METADATA_FILE_EXTENSION);
            
            boolean scriptDeleted = !scriptFile.exists() || scriptFile.delete();
            boolean metaDeleted = !metaFile.exists() || metaFile.delete();
            
            return scriptDeleted && metaDeleted;
        } catch (Exception e) {
            Log.e(TAG, "Failed to delete script: " + scriptId, e);
            return false;
        }
    }
    
    @Override
    public boolean scriptExists(String scriptId) {
        if (scriptId == null) {
            return false;
        }
        
        File scriptFile = new File(scriptsDir, scriptId + ScheduledScriptConstants.SCRIPT_FILE_EXTENSION);
        return scriptFile.exists() && scriptFile.isFile();
    }
    
    @Override
    public boolean saveScriptMetadata(String scriptId, String metadata) {
        if (scriptId == null || metadata == null) {
            return false;
        }
        
        try {
            File metaFile = new File(scriptsDir, scriptId + ScheduledScriptConstants.METADATA_FILE_EXTENSION);
            return writeFile(metaFile, metadata);
        } catch (Exception e) {
            Log.e(TAG, "Failed to save script metadata: " + scriptId, e);
            return false;
        }
    }
    
    @Override
    public String loadScriptMetadata(String scriptId) {
        if (scriptId == null) {
            return null;
        }
        
        try {
            File metaFile = new File(scriptsDir, scriptId + ScheduledScriptConstants.METADATA_FILE_EXTENSION);
            return readFile(metaFile);
        } catch (Exception e) {
            Log.e(TAG, "Failed to load script metadata: " + scriptId, e);
            return null;
        }
    }
    
    @Override
    public List<String> getAllScriptIds() {
        List<String> scriptIds = new ArrayList<>();
        
        try {
            File[] files = scriptsDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile() && file.getName().endsWith(ScheduledScriptConstants.SCRIPT_FILE_EXTENSION)) {
                        String fileName = file.getName();
                        String scriptId = fileName.substring(0, fileName.length() - ScheduledScriptConstants.SCRIPT_FILE_EXTENSION.length());
                        scriptIds.add(scriptId);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to get all script IDs", e);
        }
        
        return scriptIds;
    }    
 
   // ========== 任务持久化相关方法 ==========
    
    @Override
    public boolean saveTask(ScriptTask task) {
        if (task == null || task.taskId == null) {
            return false;
        }
        
        try {
            File taskFile = new File(tasksDir, task.taskId + ScheduledScriptConstants.TASK_FILE_EXTENSION);
            String taskJson = gson.toJson(task);
            return writeFile(taskFile, taskJson);
        } catch (Exception e) {
            Log.e(TAG, "Failed to save task: " + task.taskId, e);
            return false;
        }
    }
    
    @Override
    public ScriptTask loadTask(String taskId) {
        if (taskId == null) {
            return null;
        }
        
        try {
            File taskFile = new File(tasksDir, taskId + ScheduledScriptConstants.TASK_FILE_EXTENSION);
            String taskJson = readFile(taskFile);
            if (taskJson != null) {
                return gson.fromJson(taskJson, ScriptTask.class);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to load task: " + taskId, e);
        }
        
        return null;
    }
    
    @Override
    public List<ScriptTask> loadAllTasks() {
        List<ScriptTask> tasks = new ArrayList<>();
        
        try {
            File[] files = tasksDir.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile() && file.getName().endsWith(ScheduledScriptConstants.TASK_FILE_EXTENSION)) {
                        try {
                            String taskJson = readFile(file);
                            if (taskJson != null) {
                                ScriptTask task = gson.fromJson(taskJson, ScriptTask.class);
                                if (task != null) {
                                    tasks.add(task);
                                }
                            }
                        } catch (Exception e) {
                            Log.w(TAG, "Failed to load task file: " + file.getName(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to load all tasks", e);
        }
        
        return tasks;
    }
    
    @Override
    public boolean deleteTask(String taskId) {
        if (taskId == null) {
            return false;
        }
        
        try {
            File taskFile = new File(tasksDir, taskId + ScheduledScriptConstants.TASK_FILE_EXTENSION);
            return !taskFile.exists() || taskFile.delete();
        } catch (Exception e) {
            Log.e(TAG, "Failed to delete task: " + taskId, e);
            return false;
        }
    }
    
    @Override
    public List<ScriptTask> loadPersistentTasks() {
        List<ScriptTask> persistentTasks = new ArrayList<>();
        List<ScriptTask> allTasks = loadAllTasks();
        
        for (ScriptTask task : allTasks) {
            if (task.config != null && task.config.persistent) {
                persistentTasks.add(task);
            }
        }
        
        return persistentTasks;
    }    

    // ========== 执行历史相关方法 ==========
    
    @Override
    public boolean saveExecutionHistory(String taskId, String executionId, long startTime, 
                                       long endTime, boolean success, String result, String error) {
        if (taskId == null || executionId == null) {
            return false;
        }
        
        try {
            File taskLogDir = new File(logsDir, taskId);
            if (!taskLogDir.exists() && !taskLogDir.mkdirs()) {
                Log.e(TAG, "Failed to create task log directory: " + taskLogDir.getAbsolutePath());
                return false;
            }
            
            Map<String, Object> historyEntry = new HashMap<>();
            historyEntry.put("executionId", executionId);
            historyEntry.put("startTime", startTime);
            historyEntry.put("endTime", endTime);
            historyEntry.put("success", success);
            historyEntry.put("result", result);
            historyEntry.put("error", error);
            historyEntry.put("duration", endTime - startTime);
            
            File historyFile = new File(taskLogDir, executionId + ScheduledScriptConstants.LOG_FILE_EXTENSION);
            String historyJson = gson.toJson(historyEntry);
            return writeFile(historyFile, historyJson);
        } catch (Exception e) {
            Log.e(TAG, "Failed to save execution history: " + taskId + "/" + executionId, e);
            return false;
        }
    }
    
    @Override
    public List<String> getExecutionHistory(String taskId, int limit) {
        List<String> history = new ArrayList<>();
        
        if (taskId == null) {
            return history;
        }
        
        try {
            File taskLogDir = new File(logsDir, taskId);
            if (!taskLogDir.exists()) {
                return history;
            }
            
            File[] files = taskLogDir.listFiles();
            if (files != null) {
                // 按修改时间排序，最新的在前
                java.util.Arrays.sort(files, (f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));
                
                int count = 0;
                for (File file : files) {
                    if (file.isFile() && file.getName().endsWith(ScheduledScriptConstants.LOG_FILE_EXTENSION)) {
                        try {
                            String historyJson = readFile(file);
                            if (historyJson != null) {
                                history.add(historyJson);
                                count++;
                                if (limit > 0 && count >= limit) {
                                    break;
                                }
                            }
                        } catch (Exception e) {
                            Log.w(TAG, "Failed to read history file: " + file.getName(), e);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to get execution history: " + taskId, e);
        }
        
        return history;
    }
    
    @Override
    public int cleanupExecutionHistory(long olderThanMs) {
        int cleanedCount = 0;
        long cutoffTime = System.currentTimeMillis() - olderThanMs;
        
        try {
            File[] taskDirs = logsDir.listFiles();
            if (taskDirs != null) {
                for (File taskDir : taskDirs) {
                    if (taskDir.isDirectory()) {
                        File[] logFiles = taskDir.listFiles();
                        if (logFiles != null) {
                            for (File logFile : logFiles) {
                                if (logFile.isFile() && logFile.lastModified() < cutoffTime) {
                                    if (logFile.delete()) {
                                        cleanedCount++;
                                    }
                                }
                            }
                            
                            // 删除空目录
                            File[] remainingFiles = taskDir.listFiles();
                            if (remainingFiles == null || remainingFiles.length == 0) {
                                taskDir.delete();
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to cleanup execution history", e);
        }
        
        return cleanedCount;
    } 
   
    // ========== 存储管理相关方法 ==========
    
    @Override
    public long getStorageUsage() {
        return calculateDirectorySize(rootDir);
    }
    
    @Override
    public int cleanup() {
        int cleanedCount = 0;
        
        try {
            // 清理临时文件
            File[] tempFiles = tempDir.listFiles();
            if (tempFiles != null) {
                for (File file : tempFiles) {
                    if (file.delete()) {
                        cleanedCount++;
                    }
                }
            }
            
            // 清理过期的执行历史
            cleanedCount += cleanupExecutionHistory(ScheduledScriptConstants.DEFAULT_HISTORY_RETENTION_MS);
            
            Log.i(TAG, "Cleanup completed, removed " + cleanedCount + " files");
        } catch (Exception e) {
            Log.e(TAG, "Failed to cleanup storage", e);
        }
        
        return cleanedCount;
    }
    
    @Override
    public void destroy() {
        // 清理资源，目前没有需要特别清理的资源
        Log.i(TAG, "Storage destroyed");
    }
    
    // ========== 私有工具方法 ==========
    
    private boolean writeFile(File file, String content) {
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(content.getBytes(StandardCharsets.UTF_8));
            fos.flush();
            return true;
        } catch (IOException e) {
            Log.e(TAG, "Failed to write file: " + file.getAbsolutePath(), e);
            return false;
        }
    }
    
    private String readFile(File file) {
        if (!file.exists() || !file.isFile()) {
            return null;
        }
        
        try (FileInputStream fis = new FileInputStream(file)) {
            byte[] buffer = new byte[(int) file.length()];
            int bytesRead = fis.read(buffer);
            if (bytesRead > 0) {
                return new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
            }
        } catch (IOException e) {
            Log.e(TAG, "Failed to read file: " + file.getAbsolutePath(), e);
        }
        
        return null;
    }
    
    private long calculateDirectorySize(File directory) {
        long size = 0;
        
        if (directory == null || !directory.exists()) {
            return size;
        }
        
        try {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        size += file.length();
                    } else if (file.isDirectory()) {
                        size += calculateDirectorySize(file);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to calculate directory size: " + directory.getAbsolutePath(), e);
        }
        
        return size;
    }
    
    private String calculateFileHash(String content) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(content.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            Log.e(TAG, "Failed to calculate file hash", e);
            return null;
        }
    }
}