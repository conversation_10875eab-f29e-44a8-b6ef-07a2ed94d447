package com.bm.atool.model;

/**
 * 任务状态枚举
 */
public enum TaskStatus {
    /**
     * 等待执行
     */
    PENDING("pending"),
    
    /**
     * 正在执行
     */
    RUNNING("running"),
    
    /**
     * 已完成
     */
    COMPLETED("completed"),
    
    /**
     * 已暂停
     */
    PAUSED("paused"),
    
    /**
     * 执行失败
     */
    FAILED("failed"),
    
    /**
     * 已取消
     */
    CANCELLED("cancelled");
    
    private final String value;
    
    TaskStatus(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    /**
     * 从字符串值获取枚举
     */
    public static TaskStatus fromValue(String value) {
        for (TaskStatus status : TaskStatus.values()) {
            if (status.value.equals(value)) {
                return status;
            }
        }
        return PENDING; // 默认值
    }
    
    /**
     * 检查是否为终态
     */
    public boolean isTerminal() {
        return this == COMPLETED || this == FAILED || this == CANCELLED;
    }
    
    /**
     * 检查是否可以执行
     */
    public boolean canExecute() {
        return this == PENDING || this == PAUSED;
    }
}