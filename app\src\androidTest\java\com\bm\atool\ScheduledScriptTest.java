package com.bm.atool;

import android.content.Context;
import android.util.Log;

import androidx.test.ext.junit.runners.AndroidJUnit4;
import androidx.test.platform.app.InstrumentationRegistry;

import com.bm.atool.model.ExecutionConfig;
import com.bm.atool.model.ScheduledScriptRequest;
import com.bm.atool.model.ScriptTask;
import com.bm.atool.model.TaskStatus;
import com.bm.atool.scheduled.ScheduledScriptConstants;
import com.bm.atool.scheduled.ScheduledScriptManager;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 定时脚本功能测试
 */
@RunWith(AndroidJUnit4.class)
public class ScheduledScriptTest {
    private static final String TAG = "ScheduledScriptTest";
    
    private Context context;
    private ScheduledScriptManager scriptManager;
    
    @Before
    public void setUp() {
        context = InstrumentationRegistry.getInstrumentation().getTargetContext();
        scriptManager = ScheduledScriptManager.getInstance(context);
        
        if (!scriptManager.isInitialized()) {
            scriptManager.initialize();
        }
        
        // 清理之前的测试数据
        cleanupTestData();
    }
    
    @After
    public void tearDown() {
        cleanupTestData();
    }
    
    private void cleanupTestData() {
        try {
            List<ScriptTask> allTasks = scriptManager.getAllTasks();
            for (ScriptTask task : allTasks) {
                if (task.scriptId != null && task.scriptId.startsWith("test_")) {
                    scriptManager.cancelTask(task.taskId);
                    scriptManager.removeScript(task.scriptId);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "清理测试数据失败", e);
        }
    }
    
    @Test
    public void testAddScript() {
        Log.d(TAG, "测试添加脚本");
        
        ScheduledScriptRequest request = createTestScriptRequest("test_add_script", 
            ScheduledScriptConstants.EXECUTION_TYPE_IMMEDIATE);
        
        String scriptId = scriptManager.addScript(request);
        
        assertNotNull("脚本ID不应为空", scriptId);
        assertTrue("脚本应该存在", scriptManager.scriptExists(scriptId));
        
        Log.d(TAG, "脚本添加成功: " + scriptId);
    }
    
    @Test
    public void testScheduleImmediateTask() {
        Log.d(TAG, "测试立即执行任务");
        
        ScheduledScriptRequest request = createTestScriptRequest("test_immediate", 
            ScheduledScriptConstants.EXECUTION_TYPE_IMMEDIATE);
        
        String scriptId = scriptManager.addScript(request);
        assertNotNull("脚本ID不应为空", scriptId);
        
        // 等待一段时间让任务执行
        try {
            Thread.sleep(3000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        List<ScriptTask> tasks = scriptManager.getTasksByScript(scriptId);
        assertFalse("应该有任务被创建", tasks.isEmpty());
        
        ScriptTask task = tasks.get(0);
        Log.d(TAG, "任务状态: " + task.status + ", 执行次数: " + task.executionCount);
        
        // 立即执行的任务应该已经完成或正在执行
        assertTrue("任务应该已经开始执行", 
            task.status == TaskStatus.COMPLETED || 
            task.status == TaskStatus.RUNNING || 
            task.status == TaskStatus.FAILED);
    }
    
    @Test
    public void testScheduleDelayedTask() {
        Log.d(TAG, "测试延时执行任务");
        
        ScheduledScriptRequest request = createTestScriptRequest("test_delayed", 
            ScheduledScriptConstants.EXECUTION_TYPE_SCHEDULED);
        
        // 设置5秒后执行
        request.config.scheduleTime = System.currentTimeMillis() + 5000;
        
        String scriptId = scriptManager.addScript(request);
        assertNotNull("脚本ID不应为空", scriptId);
        
        List<ScriptTask> tasks = scriptManager.getTasksByScript(scriptId);
        assertFalse("应该有任务被创建", tasks.isEmpty());
        
        ScriptTask task = tasks.get(0);
        assertEquals("任务应该处于等待状态", TaskStatus.PENDING, task.status);
        assertTrue("下次执行时间应该在未来", task.nextExecutionTime > System.currentTimeMillis());
        
        Log.d(TAG, "延时任务创建成功，下次执行时间: " + task.nextExecutionTime);
    }
    
    @Test
    public void testRepeatTask() {
        Log.d(TAG, "测试重复执行任务");
        
        ScheduledScriptRequest request = createTestScriptRequest("test_repeat", 
            ScheduledScriptConstants.EXECUTION_TYPE_REPEAT);
        
        // 设置每2秒执行一次，最多执行3次
        request.config.repeatInterval = 2000;
        request.config.maxExecutions = 3;
        
        String scriptId = scriptManager.addScript(request);
        assertNotNull("脚本ID不应为空", scriptId);
        
        List<ScriptTask> tasks = scriptManager.getTasksByScript(scriptId);
        assertFalse("应该有任务被创建", tasks.isEmpty());
        
        ScriptTask task = tasks.get(0);
        assertEquals("任务应该处于等待状态", TaskStatus.PENDING, task.status);
        assertEquals("重复间隔应该正确", 2000, task.config.repeatInterval);
        assertEquals("最大执行次数应该正确", 3, task.config.maxExecutions);
        
        Log.d(TAG, "重复任务创建成功");
    }
    
    @Test
    public void testTaskCancellation() {
        Log.d(TAG, "测试任务取消");
        
        ScheduledScriptRequest request = createTestScriptRequest("test_cancel", 
            ScheduledScriptConstants.EXECUTION_TYPE_SCHEDULED);
        
        // 设置10秒后执行
        request.config.scheduleTime = System.currentTimeMillis() + 10000;
        
        String scriptId = scriptManager.addScript(request);
        assertNotNull("脚本ID不应为空", scriptId);
        
        List<ScriptTask> tasks = scriptManager.getTasksByScript(scriptId);
        assertFalse("应该有任务被创建", tasks.isEmpty());
        
        String taskId = tasks.get(0).taskId;
        
        // 取消任务
        boolean cancelled = scriptManager.cancelTask(taskId);
        assertTrue("任务应该被成功取消", cancelled);
        
        ScriptTask task = scriptManager.getTask(taskId);
        if (task != null) {
            assertEquals("任务状态应该是已取消", TaskStatus.CANCELLED, task.status);
        }
        
        Log.d(TAG, "任务取消成功");
    }
    
    @Test
    public void testScriptRemoval() {
        Log.d(TAG, "测试脚本删除");
        
        ScheduledScriptRequest request = createTestScriptRequest("test_remove", 
            ScheduledScriptConstants.EXECUTION_TYPE_IMMEDIATE);
        
        String scriptId = scriptManager.addScript(request);
        assertNotNull("脚本ID不应为空", scriptId);
        assertTrue("脚本应该存在", scriptManager.scriptExists(scriptId));
        
        // 删除脚本
        boolean removed = scriptManager.removeScript(scriptId);
        assertTrue("脚本应该被成功删除", removed);
        assertFalse("脚本不应该再存在", scriptManager.scriptExists(scriptId));
        
        // 相关任务也应该被删除
        List<ScriptTask> tasks = scriptManager.getTasksByScript(scriptId);
        assertTrue("相关任务应该被删除", tasks.isEmpty());
        
        Log.d(TAG, "脚本删除成功");
    }
    
    @Test
    public void testManagerStatus() {
        Log.d(TAG, "测试管理器状态");
        
        assertTrue("管理器应该已初始化", scriptManager.isInitialized());
        
        String statusInfo = scriptManager.getStatusInfo();
        assertNotNull("状态信息不应为空", statusInfo);
        assertTrue("状态信息应该包含初始化状态", statusInfo.contains("Initialized: true"));
        
        Log.d(TAG, "管理器状态: " + statusInfo);
    }
    
    /**
     * 创建测试脚本请求
     */
    private ScheduledScriptRequest createTestScriptRequest(String scriptId, String executionType) {
        ScheduledScriptRequest request = new ScheduledScriptRequest();
        request.scriptId = scriptId;
        request.scriptName = "测试脚本 - " + scriptId;
        request.scriptContent = "console.log('Hello from test script: " + scriptId + "');";
        request.description = "这是一个测试脚本";
        request.version = "1.0.0";
        
        // 创建执行配置
        ExecutionConfig config = new ExecutionConfig();
        config.executionType = executionType;
        config.timeout = 10000; // 10秒超时
        config.persistent = false; // 测试脚本不持久化
        
        // 添加测试参数
        Map<String, Object> params = new HashMap<>();
        params.put("testParam", "testValue");
        params.put("timestamp", System.currentTimeMillis());
        
        request.config = config;
        request.params = params;
        
        return request;
    }
}