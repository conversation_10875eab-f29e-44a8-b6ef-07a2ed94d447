package com.bm.atool.scheduled;

import android.content.Context;
import android.util.Log;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 脚本下载器
 * 支持重试机制和错误处理
 */
public class ScriptDownloader {
    private static final String TAG = ScheduledScriptConstants.TAG_SCRIPT_DOWNLOADER;
    
    private final Context context;
    private final ExecutorService executorService;
    
    public ScriptDownloader(Context context) {
        this.context = context.getApplicationContext();
        this.executorService = Executors.newCachedThreadPool();
    }
    
    /**
     * 下载脚本回调接口
     */
    public interface DownloadCallback {
        void onSuccess(String scriptContent);
        void onFailure(String error);
        void onProgress(int progress);
    }
    
    /**
     * 异步下载脚本
     * @param url 下载URL
     * @param callback 回调接口
     */
    public void downloadScript(String url, DownloadCallback callback) {
        downloadScriptWithRetry(url, ScheduledScriptConstants.MAX_DOWNLOAD_RETRIES, callback);
    }
    
    /**
     * 带重试的脚本下载
     * @param url 下载URL
     * @param maxRetries 最大重试次数
     * @param callback 回调接口
     */
    public void downloadScriptWithRetry(String url, int maxRetries, DownloadCallback callback) {
        executorService.submit(() -> {
            int retryCount = 0;
            String lastError = null;
            
            while (retryCount <= maxRetries) {
                try {
                    Log.d(TAG, "开始下载脚本: " + url + ", 尝试次数: " + (retryCount + 1));
                    
                    String content = downloadScriptSync(url, callback);
                    if (content != null && !content.isEmpty()) {
                        Log.i(TAG, "脚本下载成功: " + url);
                        callback.onSuccess(content);
                        return;
                    } else {
                        lastError = "下载内容为空";
                    }
                    
                } catch (Exception e) {
                    lastError = e.getMessage();
                    Log.w(TAG, "脚本下载失败 (尝试 " + (retryCount + 1) + "/" + (maxRetries + 1) + "): " + lastError);
                }
                
                retryCount++;
                
                // 如果还有重试机会，等待一段时间后重试
                if (retryCount <= maxRetries) {
                    try {
                        long delay = ScheduledScriptConstants.DOWNLOAD_RETRY_DELAY_MS * retryCount; // 递增延迟
                        Log.d(TAG, "等待 " + delay + "ms 后重试下载");
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        callback.onFailure("下载被中断");
                        return;
                    }
                }
            }
            
            // 所有重试都失败了
            String finalError = "下载失败，已重试 " + maxRetries + " 次。最后错误: " + lastError;
            Log.e(TAG, finalError);
            callback.onFailure(finalError);
        });
    }
    
    /**
     * 同步下载脚本
     * @param url 下载URL
     * @param callback 用于报告进度的回调
     * @return 脚本内容
     * @throws IOException 下载异常
     */
    private String downloadScriptSync(String url, DownloadCallback callback) throws IOException {
        HttpURLConnection connection = null;
        InputStream inputStream = null;
        BufferedReader reader = null;
        
        try {
            // 创建连接
            URL downloadUrl = new URL(url);
            connection = (HttpURLConnection) downloadUrl.openConnection();
            
            // 设置请求属性
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(10000); // 10秒连接超时
            connection.setReadTimeout(30000);    // 30秒读取超时
            connection.setRequestProperty("User-Agent", "AndroidTool/1.0");
            connection.setRequestProperty("Accept", "text/plain, application/javascript, */*");
            
            // 连接
            connection.connect();
            
            // 检查响应码
            int responseCode = connection.getResponseCode();
            if (responseCode != HttpURLConnection.HTTP_OK) {
                throw new IOException("HTTP错误码: " + responseCode + " " + connection.getResponseMessage());
            }
            
            // 获取内容长度
            int contentLength = connection.getContentLength();
            Log.d(TAG, "脚本内容长度: " + contentLength + " bytes");
            
            // 检查内容长度限制
            if (contentLength > ScheduledScriptConstants.MAX_SCRIPT_SIZE_BYTES) {
                throw new IOException("脚本文件过大: " + contentLength + " bytes，最大允许: " + 
                                    ScheduledScriptConstants.MAX_SCRIPT_SIZE_BYTES + " bytes");
            }
            
            // 读取内容
            inputStream = connection.getInputStream();
            reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            
            StringBuilder content = new StringBuilder();
            String line;
            int totalBytesRead = 0;
            int lastProgress = 0;
            
            while ((line = reader.readLine()) != null) {
                content.append(line).append('\n');
                totalBytesRead += line.getBytes(StandardCharsets.UTF_8).length + 1; // +1 for newline
                
                // 报告进度
                if (contentLength > 0) {
                    int progress = (int) ((totalBytesRead * 100L) / contentLength);
                    if (progress > lastProgress + 10) { // 每10%报告一次
                        callback.onProgress(progress);
                        lastProgress = progress;
                    }
                }
                
                // 检查大小限制
                if (totalBytesRead > ScheduledScriptConstants.MAX_SCRIPT_SIZE_BYTES) {
                    throw new IOException("脚本内容超过大小限制");
                }
            }
            
            callback.onProgress(100); // 完成
            
            String result = content.toString();
            
            // 验证脚本内容
            if (result.trim().isEmpty()) {
                throw new IOException("下载的脚本内容为空");
            }
            
            // 简单的脚本格式验证
            if (!isValidScriptContent(result)) {
                Log.w(TAG, "脚本内容可能不是有效的JavaScript代码");
            }
            
            return result;
            
        } finally {
            // 清理资源
            if (reader != null) {
                try { reader.close(); } catch (IOException e) { /* ignore */ }
            }
            if (inputStream != null) {
                try { inputStream.close(); } catch (IOException e) { /* ignore */ }
            }
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 简单验证脚本内容是否有效
     * @param content 脚本内容
     * @return 是否有效
     */
    private boolean isValidScriptContent(String content) {
        if (content == null || content.trim().isEmpty()) {
            return false;
        }

        // 检查是否包含明显的恶意内容
        String lowerContent = content.toLowerCase();
        String[] suspiciousPatterns = {
            "rm -rf", "format", "delete", "destroy", 
            "virus", "malware", "hack", "exploit"
        };

        for (String pattern : suspiciousPatterns) {
            if (lowerContent.contains(pattern)) {
                Log.w(TAG, "脚本内容包含可疑模式: " + pattern);
                return false;
            }
        }

        return true;
    }

    /**
     * 销毁下载器
     */
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}