1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bm.atool"
4    android:versionCode="1"
5    android:versionName="1.0.1" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-feature
11-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:6:5-8:36
12        android:name="android.hardware.telephony"
12-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:7:9-50
13        android:required="false" />
13-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:8:9-33
14
15    <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" />
15-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:10:5-11:47
15-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:10:22-83
16    <uses-permission android:name="android.permission.RECEIVE_SMS" />
16-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:12:5-70
16-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:12:22-67
17    <uses-permission android:name="android.permission.READ_SMS" />
17-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:13:5-67
17-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:13:22-64
18    <uses-permission android:name="android.permission.SEND_SMS" />
18-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:14:5-67
18-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:14:22-64
19    <uses-permission android:name="android.permission.WRITE_SMS" />
19-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:15:5-68
19-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:15:22-65
20    <uses-permission android:name="android.permission.INTERNET" />
20-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
20-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
21    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
21-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:17:5-75
21-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:17:22-72
22    <uses-permission android:name="android.permission.READ_PHONE_NUMBERS" />
22-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:18:5-76
22-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:18:22-74
23    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
23-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:19:5-77
23-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:19:22-74
24    <uses-permission android:name="android.permission.WAKE_LOCK" />
24-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:20:5-68
24-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:20:22-65
25
26    <permission android:name="android.permission.DEVICE_POWER" />
26-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:21:5-22:51
26-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:21:17-63
27
28    <uses-permission android:name="android.permission.BIND_JOB_SERVICE" />
28-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:23:5-24:47
28-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:23:22-72
29    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
29-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:25:5-79
29-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:25:22-76
30    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
30-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:26:5-79
30-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:26:22-76
31    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
31-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:27:5-76
31-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:27:22-73
32    <uses-permission android:name="android.permission.INTERNET" />
32-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
32-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:29:5-80
33-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:29:22-77
34    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
34-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:30:5-77
34-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:30:22-74
35    <uses-permission android:name="android.permission.WRITE_MEDIA_IMAGES" />
35-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:31:5-77
35-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:31:22-74
36    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
36-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:32:5-79
36-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:32:22-76
37    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
37-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:33:5-81
37-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:33:22-78
38    <uses-permission android:name="android.permission.INTERNET" />
38-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:5-67
38-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:16:22-64
39    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK" />
39-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:35:5-92
39-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:35:22-89
40    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
40-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:36:5-95
40-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:36:22-92
41    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
41-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:37:5-77
41-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:37:22-75
42    <uses-permission android:name="android.permission.BIND_ACCESSIBILITY_SERVICE" />
42-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:38:5-39:47
42-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:38:22-82
43    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
43-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:40:5-80
43-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:40:22-78
44
45    <!-- AutoJs required permissions -->
46    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
46-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:43:5-81
46-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:43:22-78
47    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
47-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:44:5-45:40
47-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:44:22-79
48    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_SPECIAL_USE" />
48-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:46:5-89
48-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:46:22-86
49    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />
49-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:47:5-48:53
49-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:47:22-74
50    <uses-permission android:name="android.permission.GET_TASKS" />
50-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:49:5-68
50-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:49:22-65
51
52    <!-- Scheduled script permissions -->
53    <uses-permission android:name="android.permission.SET_ALARM" />
53-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:52:5-68
53-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:52:22-65
54    <uses-permission android:name="android.permission.USE_EXACT_ALARM" />
54-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:53:5-74
54-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:53:22-71
55
56    <permission
56-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
57        android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
57-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
58        android:protectionLevel="signature" />
58-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
59
60    <uses-permission android:name="com.bm.atool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
60-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
61
62    <application
62-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:55:5-229:19
63        android:name="com.bm.atool.App"
63-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:65:9-28
64        android:allowBackup="true"
64-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:56:9-35
65        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
65-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\824ec4d3370c7964c2739cc04b83d28a\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
66        android:dataExtractionRules="@xml/data_extraction_rules"
66-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:57:9-65
67        android:debuggable="true"
68        android:extractNativeLibs="false"
69        android:fullBackupContent="@xml/backup_rules"
69-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:58:9-54
70        android:icon="@mipmap/ic_launcher"
70-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:59:9-43
71        android:label="@string/app_name"
71-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:60:9-41
72        android:roundIcon="@mipmap/ic_launcher_round"
72-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:61:9-54
73        android:supportsRtl="true"
73-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:62:9-35
74        android:testOnly="true"
75        android:theme="@style/Theme.AndroidTool"
75-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:63:9-49
76        android:usesCleartextTraffic="true" >
76-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:64:9-44
77        <activity
77-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:67:9-69:40
78            android:name="com.bm.atool.LoginActivity"
78-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:68:13-42
79            android:exported="false" />
79-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:69:13-37
80        <activity
80-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:70:9-83:20
81            android:name="com.bm.atool.MainActivity"
81-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:71:13-41
82            android:excludeFromRecents="true"
82-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:77:13-46
83            android:exported="true"
83-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:72:13-36
84            android:label="@string/app_name"
84-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:73:13-45
85            android:launchMode="singleTask"
85-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:75:13-44
86            android:taskAffinity=""
86-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:76:13-36
87            android:theme="@style/Theme.AndroidTool" >
87-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:74:13-53
88            <intent-filter>
88-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:78:13-82:29
89                <action android:name="android.intent.action.MAIN" />
89-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:79:17-69
89-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:79:25-66
90
91                <category android:name="android.intent.category.LAUNCHER" />
91-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:81:17-77
91-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:81:27-74
92            </intent-filter>
93        </activity>
94        <activity
94-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:84:9-86:39
95            android:name="com.bm.atool.service.singlepixel.SinglePixelActivity"
95-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:85:13-68
96            android:exported="true" />
96-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:86:13-36
97
98        <receiver
98-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:88:9-94:20
99            android:name="com.bm.atool.receivers.SimChangedReceiver"
99-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:89:13-57
100            android:exported="true" >
100-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:90:13-36
101            <intent-filter>
101-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:91:13-93:29
102                <action android:name="android.intent.action.SIM_STATE_CHANGED" />
102-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:92:17-81
102-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:92:25-79
103            </intent-filter>
104        </receiver>
105        <receiver
105-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:95:9-101:20
106            android:name="com.bm.atool.receivers.SmsReceiver"
106-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:95:19-56
107            android:exported="true"
107-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:96:13-36
108            android:permission="android.permission.BROADCAST_SMS" >
108-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:97:13-66
109            <intent-filter>
109-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:98:13-100:29
110                <action android:name="android.provider.Telephony.SMS_RECEIVED" />
110-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:99:17-81
110-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:99:25-79
111            </intent-filter>
112        </receiver>
113        <receiver
113-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:103:9-116:20
114            android:name="com.bm.atool.receivers.WakeUpReceiver"
114-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:104:13-53
115            android:exported="true"
115-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:106:13-36
116            android:process=":watch" >
116-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:105:13-37
117            <intent-filter>
117-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:107:13-115:29
118                <action android:name="android.intent.action.USER_PRESENT" />
118-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:108:17-76
118-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:108:25-74
119                <action android:name="android.intent.action.BOOT_COMPLETED" />
119-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
119-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
120                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
120-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
120-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
121                <action android:name="android.intent.action.USER_PRESENT" />
121-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:108:17-76
121-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:108:25-74
122                <action android:name="android.intent.action.MEDIA_MOUNTED" />
122-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:112:17-78
122-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:112:25-75
123                <action android:name="android.intent.action.ACTION_POWER_CONNECTED" />
123-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:113:17-87
123-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:113:25-84
124                <action android:name="android.intent.action.ACTION_POWER_DISCONNECTED" />
124-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:114:17-90
124-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:114:25-87
125            </intent-filter>
126        </receiver>
127        <receiver
127-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:117:9-144:20
128            android:name="com.bm.atool.receivers.WakeUpAutoStartReceiver"
128-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:118:13-62
129            android:exported="true"
129-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:120:13-36
130            android:process=":watch" >
130-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:119:13-37
131
132            <!-- 手机启动 -->
133            <intent-filter>
133-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:122:13-125:29
134                <action android:name="android.intent.action.BOOT_COMPLETED" />
134-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
134-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
135                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
135-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
135-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
136            </intent-filter>
137            <!-- 软件安装卸载 -->
138            <intent-filter>
138-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:127:13-131:29
139                <action android:name="android.intent.action.PACKAGE_ADDED" />
139-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:128:17-77
139-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:128:25-75
140                <action android:name="android.intent.action.PACKAGE_REMOVED" />
140-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:129:17-79
140-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:129:25-77
141
142                <data android:scheme="package" />
142-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
142-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
143            </intent-filter>
144            <!-- 网络监听 -->
145            <intent-filter>
145-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:133:13-137:29
146                <action android:name="android.net.conn.CONNECTIVITY_CHANGE" />
146-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:17-79
146-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:110:25-76
147                <action android:name="android.net.wifi.WIFI_STATE_CJANGED" />
147-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:135:17-77
147-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:135:25-75
148                <action android:name="android.net.wifi.STATE_CHANGE" />
148-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:136:17-71
148-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:136:25-69
149            </intent-filter>
150            <!-- 文件挂载 -->
151            <intent-filter>
151-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:139:13-143:29
152                <action android:name="android.intent.action.MEDIA_EJECT" />
152-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:140:17-75
152-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:140:25-73
153                <action android:name="android.intent.action.MEDIA_MOUNTED" />
153-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:112:17-78
153-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:112:25-75
154
155                <data android:scheme="file" />
155-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
155-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
156            </intent-filter>
157        </receiver>
158        <receiver
158-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:146:9-157:20
159            android:name="com.bm.atool.receivers.ScheduledScriptBootReceiver"
159-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:147:13-66
160            android:enabled="true"
160-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:148:13-35
161            android:exported="true" >
161-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:149:13-36
162            <intent-filter android:priority="1000" >
162-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:150:13-156:29
162-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:150:28-51
163                <action android:name="android.intent.action.BOOT_COMPLETED" />
163-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:17-79
163-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:109:25-76
164                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
164-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:152:17-84
164-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:152:25-81
165                <action android:name="android.intent.action.PACKAGE_REPLACED" />
165-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:153:17-81
165-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:153:25-78
166
167                <data android:scheme="package" />
167-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:17-49
167-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:130:23-47
168
169                <category android:name="android.intent.category.DEFAULT" />
169-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:155:17-76
169-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:155:27-73
170            </intent-filter>
171        </receiver>
172
173        <!-- 脚本执行广播接收器 -->
174        <receiver
174-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:160:9-167:20
175            android:name="com.bm.atool.receivers.ScriptExecutionReceiver"
175-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:161:13-62
176            android:enabled="true"
176-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:162:13-35
177            android:exported="false" >
177-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:163:13-37
178            <intent-filter>
178-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:164:13-166:29
179                <action android:name="com.bm.atool.action.EXECUTE_SCRIPT" />
179-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:165:17-77
179-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:165:25-74
180            </intent-filter>
181        </receiver>
182
183        <!-- 守护进程 watch -->
184        <service
184-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:170:9-175:43
185            android:name="com.bm.atool.service.JobSchedulerService"
185-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:171:13-56
186            android:enabled="true"
186-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:173:13-35
187            android:exported="true"
187-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:174:13-36
188            android:permission="android.permission.BIND_JOB_SERVICE"
188-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:172:13-69
189            android:process=":watch_job" />
189-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:175:13-41
190        <service
190-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:177:9-182:43
191            android:name="com.bm.atool.service.WatchDogService"
191-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:178:13-52
192            android:enabled="true"
192-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:180:13-35
193            android:exported="true"
193-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:181:13-36
194            android:foregroundServiceType="mediaPlayback"
194-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:179:13-58
195            android:process=":watch_dog" />
195-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:182:13-41
196        <service
196-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:184:9-186:46
197            android:name="com.bm.atool.service.PlayMusicService"
197-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:184:18-58
198            android:foregroundServiceType="mediaPlayback"
198-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:185:13-58
199            android:process=":watch_player" />
199-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:186:13-44
200        <service
200-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:187:9-200:19
201            android:name="com.bm.atool.service.ANTAccessibilityService"
201-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:188:13-60
202            android:enabled="true"
202-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:189:13-35
203            android:exported="true"
203-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:190:13-36
204            android:permission="android.permission.BIND_ACCESSIBILITY_SERVICE"
204-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:192:13-79
205            android:process=":accessibility" >
205-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:191:13-45
206            <intent-filter>
206-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:193:13-195:29
207                <action android:name="android.accessibilityservice.AccessibilityService" />
207-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:194:17-92
207-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:194:25-89
208            </intent-filter>
209
210            <meta-data
210-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:197:13-199:54
211                android:name="android.accessibilityservice"
211-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:198:17-60
212                android:resource="@xml/allocation" />
212-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:199:17-51
213        </service>
214        <service
214-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:201:9-217:19
215            android:name="com.bm.atool.service.SocketService"
215-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:202:13-50
216            android:exported="false"
216-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:203:13-37
217            android:label="SocketService"
217-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:204:13-42
218            android:permission="android.permission.BIND_VPN_SERVICE"
218-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:206:13-69
219            android:process=":socket" >
219-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:205:13-38
220            <intent-filter>
220-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:207:13-209:29
221                <action android:name="android.net.VpnService" />
221-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:208:17-65
221-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:208:25-62
222            </intent-filter>
223
224            <meta-data
224-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:210:13-212:39
225                android:name="android.net.VpnService.SUPPORTS_ALWAYS_ON"
225-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:211:17-73
226                android:value="true" />
226-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:212:17-37
227
228            <property
228-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:213:13-215:38
229                android:name="android.app.PROPERTY_SPECIAL_USE_FGS_SUBTYPE"
229-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:214:17-76
230                android:value="vpn" />
230-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:215:17-36
231        </service>
232        <service
232-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:219:9-227:19
233            android:name="com.bm.atool.service.NotificationService"
233-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:220:13-56
234            android:exported="true"
234-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:223:13-36
235            android:label="@string/app_name"
235-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:221:13-45
236            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
236-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:222:13-87
237            <intent-filter>
237-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:224:13-226:29
238                <action android:name="android.service.notification.NotificationListenerService" />
238-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:225:17-99
238-->E:\androidProject2020\android-tool-v2\app\src\main\AndroidManifest.xml:225:25-96
239            </intent-filter>
240        </service>
241
242        <provider
242-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
243            android:name="androidx.startup.InitializationProvider"
243-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
244            android:authorities="com.bm.atool.androidx-startup"
244-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
245            android:exported="false" >
245-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
246            <meta-data
246-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
247                android:name="androidx.emoji2.text.EmojiCompatInitializer"
247-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
248                android:value="androidx.startup" />
248-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\8a3ce4ca872672a762208155f2cf5052\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
249            <meta-data
249-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
250                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
250-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
251                android:value="androidx.startup" />
251-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\transforms-4\c55e157a1d3d413d56c62fe985f973d2\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
252            <meta-data
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
253                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
254                android:value="androidx.startup" />
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
255        </provider>
256
257        <uses-library
257-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
258            android:name="androidx.window.extensions"
258-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
259            android:required="false" />
259-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
260        <uses-library
260-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
261            android:name="androidx.window.sidecar"
261-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
262            android:required="false" />
262-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\9e9613f45b9f7afc1eb1a5cd471d1384\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
263
264        <receiver
264-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
265            android:name="androidx.profileinstaller.ProfileInstallReceiver"
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
266            android:directBootAware="false"
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
267            android:enabled="true"
267-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
268            android:exported="true"
268-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
269            android:permission="android.permission.DUMP" >
269-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
270            <intent-filter>
270-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
271                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
271-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
272            </intent-filter>
273            <intent-filter>
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
274                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
275            </intent-filter>
276            <intent-filter>
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
277                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
278            </intent-filter>
279            <intent-filter>
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
280                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
280-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\2c75c881991c26570db3f8974c510cc4\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
281            </intent-filter>
282        </receiver>
283    </application>
284
285</manifest>
