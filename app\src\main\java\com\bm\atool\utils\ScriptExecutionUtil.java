package com.bm.atool.utils;

import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.util.Log;
import android.widget.Toast;

import com.bm.atool.service.ANTAccessibilityService;

/**
 * 脚本执行工具类
 * 提供便捷的脚本执行方法
 */
public class ScriptExecutionUtil {
    private static final String TAG = "ScriptExecutionUtil";
    
    /**
     * 启动模拟脚本执行
     * 这个方法会通过无障碍服务模拟下载和执行脚本
     * @param context 上下文
     * @return 是否成功启动脚本执行
     */
    public static boolean executeSimulatedScript(Context context) {
        Log.d(TAG, "executeSimulatedScript called");
        boolean success = ANTAccessibilityService.startScriptExecution();
        Log.d(TAG, "ANTAccessibilityService.startScriptExecution() returned: " + success);
        
        if (success) {
            Log.d(TAG, "脚本执行已启动");
            showToast(context, "脚本执行已启动");
        } else {
            Log.e(TAG, "脚本执行启动失败，请确认无障碍服务已启用");
            showToast(context, "脚本执行启动失败，请前往设置->无障碍->ATool启用服务");
            
            // 引导用户去启用无障碍服务
            if (context != null) {
                try {
                    Intent intent = new Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS);
                    intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    context.startActivity(intent);
                } catch (Exception e) {
                    Log.e(TAG, "无法启动无障碍设置页面: " + e.getMessage());
                }
            }
        }
        
        Log.d(TAG, "executeSimulatedScript completed with success: " + success);
        return success;
    }
    
    /**
     * 显示Toast消息
     * @param context 上下文
     * @param message 消息内容
     */
    private static void showToast(Context context, String message) {
        if (context != null) {
            Toast.makeText(context, message, Toast.LENGTH_LONG).show();
        }
    }
}