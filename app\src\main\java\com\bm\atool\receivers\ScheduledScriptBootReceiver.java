package com.bm.atool.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bm.atool.scheduled.ScheduledScriptManager;

/**
 * 系统启动广播接收器
 * 用于在系统重启后恢复定时脚本任务
 */
public class ScheduledScriptBootReceiver extends BroadcastReceiver {
    private static final String TAG = "ScheduledScriptBootReceiver";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        if (Intent.ACTION_BOOT_COMPLETED.equals(intent.getAction()) ||
            Intent.ACTION_MY_PACKAGE_REPLACED.equals(intent.getAction()) ||
            Intent.ACTION_PACKAGE_REPLACED.equals(intent.getAction())) {
            
            Log.d(TAG, "收到系统启动或应用更新广播: " + intent.getAction());
            
            try {
                // 延迟初始化，确保系统完全启动
                android.os.Handler handler = new android.os.Handler(android.os.Looper.getMainLooper());
                handler.postDelayed(() -> {
                    try {
                        Log.d(TAG, "开始恢复定时脚本任务");
                        
                        ScheduledScriptManager manager = ScheduledScriptManager.getInstance(context);
                        if (!manager.isInitialized()) {
                            manager.initialize();
                            Log.i(TAG, "定时脚本管理器初始化并恢复任务成功");
                        } else {
                            Log.d(TAG, "定时脚本管理器已经初始化");
                        }
                        
                    } catch (Exception e) {
                        Log.e(TAG, "恢复定时脚本任务失败", e);
                    }
                }, 10000); // 延迟10秒启动
                
            } catch (Exception e) {
                Log.e(TAG, "处理系统启动广播失败", e);
            }
        }
    }
}