package com.bm.atool.receivers;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.bm.atool.service.ANTAccessibilityService;

/**
 * 脚本执行广播接收器
 * 用于接收跨进程的脚本执行广播，并通知ANTAccessibilityService执行脚本
 */
public class ScriptExecutionReceiver extends BroadcastReceiver {
    private static final String TAG = "ScriptExecutionReceiver";
    
    public static final String ACTION_EXECUTE_SCRIPT = "com.bm.atool.action.EXECUTE_SCRIPT";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "ScriptExecutionReceiver.onReceive called");
        if (ACTION_EXECUTE_SCRIPT.equals(intent.getAction())) {
            Log.d(TAG, "接收到执行脚本广播");
            
            // 通过文件方式触发脚本执行（跨进程安全的方式）
            try {
                Log.d(TAG, "准备创建脚本执行标志文件");
                
                // 创建文件标志，让无障碍服务检测到后执行脚本
                java.io.File flagFile = new java.io.File(context.getFilesDir(), "execute_script_flag.txt");
                try (java.io.FileWriter writer = new java.io.FileWriter(flagFile)) {
                    writer.write(String.valueOf(System.currentTimeMillis()));
                    writer.flush();
                }
                Log.d(TAG, "已创建执行脚本标志文件: " + flagFile.getAbsolutePath());
            } catch (Exception e) {
                Log.e(TAG, "创建脚本执行标志文件时发生异常: " + e.getMessage(), e);
            }
        } else {
            Log.d(TAG, "接收到未知广播: " + intent.getAction());
        }
    }
}