package com.bm.atool.ui;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;


import com.bm.atool.R;
import com.bm.atool.Sys;
import com.bm.atool.service.SocketService;
import com.bm.atool.service.WatchDogService;

import java.util.List;

public class DebugFragment extends BaseFragment{
    private static final String TAG = "DebugFragment";
    public DebugFragment(){
        setTitle("DEBUG");
    }
    private Button btnStopSocket;
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        // Initialize view
        View view =inflater.inflate(R.layout.fragment_debug, container, false);
        btnStopSocket = view.findViewById(R.id.btnStopSocket);
        btnStopSocket.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                Log.d(TAG, "Stopping all services from DebugFragment");
                Sys.stop();
                Log.d(TAG, "Sys.stop() called.");
            }
        });


        return view;
    }

    @Override
    public int getIconResourceId() {
        return R.drawable.tab_icon_debug;
    }
}
