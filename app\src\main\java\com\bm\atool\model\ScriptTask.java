package com.bm.atool.model;

/**
 * 脚本任务模型
 * 表示一个具体的脚本执行任务
 */
public class ScriptTask {
    /**
     * 任务唯一标识
     */
    public String taskId;
    
    /**
     * 关联的脚本ID
     */
    public String scriptId;
    
    /**
     * 执行配置
     */
    public ExecutionConfig config;
    
    /**
     * 任务状态
     */
    public TaskStatus status;
    
    /**
     * 创建时间
     */
    public long createdTime;
    
    /**
     * 最后执行时间
     */
    public long lastExecutionTime;
    
    /**
     * 下次执行时间（用于重复任务）
     */
    public long nextExecutionTime;
    
    /**
     * 已执行次数
     */
    public int executionCount;
    
    /**
     * 最后错误信息
     */
    public String lastError;
    
    /**
     * 最后执行结果
     */
    public String lastResult;
    
    /**
     * 当前执行ID（如果正在执行）
     */
    public String currentExecutionId;
    
    /**
     * 任务创建者（用于权限控制）
     */
    public String creator;
    
    /**
     * 构造函数
     */
    public ScriptTask() {
        this.status = TaskStatus.PENDING;
        this.createdTime = System.currentTimeMillis();
        this.executionCount = 0;
    }
    
    /**
     * 检查任务是否可以执行
     */
    public boolean canExecute() {
        if (status == TaskStatus.CANCELLED || status == TaskStatus.COMPLETED) {
            return false;
        }
        
        if (status == TaskStatus.RUNNING && !config.allowConcurrent) {
            return false;
        }
        
        if (config.maxExecutions > 0 && executionCount >= config.maxExecutions) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 更新下次执行时间
     */
    public void updateNextExecutionTime() {
        if ("repeat".equals(config.executionType) && config.repeatInterval > 0) {
            this.nextExecutionTime = System.currentTimeMillis() + config.repeatInterval;
        }
    }
}