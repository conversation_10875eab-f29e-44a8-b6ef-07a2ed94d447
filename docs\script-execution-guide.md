# ATool 脚本执行功能使用指南

## 概述

ATool 提供了强大的脚本执行功能，允许用户自动化执行各种操作。本文档将详细介绍如何使用这些功能。

## 功能特性

1. **无障碍服务支持** - 通过无障碍服务执行点击、滑动等操作
2. **JavaScript引擎** - 使用JavaScript编写自动化脚本
3. **设备信息访问** - 获取屏幕尺寸等设备信息
4. **网络通信** - 通过Socket与服务器通信
5. **定时任务** - 支持定时执行脚本

## 启用无障碍服务

为了使用脚本执行功能，必须先启用ATool的无障碍服务：

1. 打开手机设置
2. 找到"无障碍"或"辅助功能"选项
3. 在服务列表中找到"ATool"
4. 点击并启用该服务
5. 确认安全提示

## 脚本API参考

### 基础API

```javascript
// 等待无障碍服务就绪
auto.waitFor();

// 显示Toast消息
toast("Hello, World!");

// 暂停执行（毫秒）
sleep(1000);

// 输出日志
console.log("This is a log message");
```

### 设备信息

```javascript
// 屏幕宽度
var screenWidth = device.width;

// 屏幕高度
var screenHeight = device.height;
```

### 操作API

```javascript
// 点击指定坐标
click(100, 200);

// 滑动操作 (起始x, 起始y, 结束x, 结束y, 持续时间ms)
swipe(100, 500, 100, 100, 500);

// 长按操作 (x, y, 持续时间ms)
longClick(150, 150, 1000);
```

## 脚本示例

### 简单点击脚本

```javascript
auto.waitFor();
toast("开始执行点击脚本");

// 点击屏幕中心
click(device.width / 2, device.height / 2);

toast("点击完成");
```

### 导航栏点击脚本

```javascript
auto.waitFor();
toast("开始执行导航栏点击脚本");

// 等待5秒
sleep(5000);

// 点击导航栏20次
for (var i = 0; i < 20; i++) {
    // 点击导航栏区域（假设在屏幕底部）
    var x = device.width / 2;
    var y = device.height - 50;
    click(x, y);
    
    // 每5次显示一次进度
    if ((i + 1) % 5 == 0) {
        toast("已完成" + (i + 1) + "次点击");
    }
    
    // 等待100毫秒
    sleep(100);
}

toast("导航栏点击完成");
```

## 通过应用界面执行脚本

1. 打开ATool应用
2. 在主界面找到"直接执行脚本"按钮
3. 点击按钮开始执行预设的模拟脚本

## 通过服务器执行脚本

服务器可以下发各种脚本到设备上执行：

1. 确保设备已连接到服务器
2. 服务器发送脚本执行指令
3. 设备自动下载并执行脚本

## 常见问题解决

### 问题1：脚本执行失败

**可能原因：**
- 无障碍服务未启用
- 脚本语法错误
- 设备不支持某些API

**解决方案：**
1. 检查并启用无障碍服务
2. 检查脚本语法
3. 确认设备兼容性

### 问题2：点击无响应

**可能原因：**
- 应用在后台被限制
- 点击坐标不正确

**解决方案：**
1. 将ATool加入电池优化白名单
2. 确认目标应用在前台
3. 检查点击坐标

## 最佳实践

1. **权限管理** - 确保应用有必要的权限
2. **错误处理** - 在脚本中添加适当的错误处理
3. **性能优化** - 避免过于频繁的操作
4. **日志记录** - 使用console.log记录执行过程
5. **测试验证** - 在不同设备上测试脚本

## 安全注意事项

1. 仅执行可信来源的脚本
2. 定期检查脚本内容
3. 注意保护个人隐私信息
4. 遵守相关法律法规

## 技术支持

如遇到问题，请联系技术支持或查看以下资源：
- GitHub项目页面
- 官方文档
- 社区论坛