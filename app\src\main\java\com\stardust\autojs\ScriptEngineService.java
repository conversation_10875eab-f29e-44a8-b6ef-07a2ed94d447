package com.stardust.autojs;

import android.util.Log;

import com.stardust.autojs.execution.ExecutionConfig;
import com.stardust.autojs.execution.ScriptExecution;
import com.stardust.autojs.execution.ScriptExecutionListener;
import com.stardust.autojs.script.StringScriptSource;
import com.stardust.autojs.engine.ScriptEngine;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class ScriptEngineService {
    private static final String TAG = "ScriptEngineService";
    
    // 记录运行中的脚本
    private final ConcurrentHashMap<String, ScriptExecution> runningExecutions = new ConcurrentHashMap<>();
    
    public ScriptExecution execute(StringScriptSource scriptSource, ExecutionConfig config, Map<String, Object> params, ScriptExecutionListener listener) {
        // 生成唯一执行ID
        String executionId = UUID.randomUUID().toString();
        
        Log.d(TAG, "开始执行脚本，ID: " + executionId);
        
        // 创建脚本引擎
        ScriptEngine engine = new ScriptEngine();
        Log.d(TAG, "脚本引擎创建完成");
        
        // 设置参数
        if (params != null) {
            engine.setParams(params);
            Log.d(TAG, "脚本参数设置完成");
        }
        
        // 创建执行对象
        ScriptExecution execution = new ScriptExecution(executionId, engine);
        runningExecutions.put(executionId, execution);
        Log.d(TAG, "脚本执行对象创建完成");
        
        // 通知开始执行
        if (listener != null) {
            Log.d(TAG, "通知脚本开始执行");
            listener.onStart(execution);
        }

        // 在后台线程执行脚本
        Thread scriptThread = new Thread(() -> {
            try {
                Log.d(TAG, "开始执行脚本内容");
                // 实际执行脚本
                engine.execute(scriptSource.getScript());
                Log.d(TAG, "脚本内容执行完成");
                
                // 执行成功回调
                if (listener != null) {
                    String output = engine.getConsoleOutput();
                    Log.d(TAG, "脚本执行成功，输出: " + output);
                    listener.onSuccess(execution, output.isEmpty() ? "Script executed successfully" : output);
                }
            } catch (Exception e) {
                Log.e(TAG, "脚本执行异常: " + e.getMessage(), e);
                // 执行异常回调
                if (listener != null) {
                    listener.onException(execution, e);
                }
            } finally {
                // 清理资源
                runningExecutions.remove(executionId);
                Log.d(TAG, "脚本执行资源清理完成");
            }
        }, "ScriptExecution-" + executionId);
        
        scriptThread.start();
        Log.d(TAG, "脚本执行线程已启动");
        
        return execution;
    }
    
    public void stopExecution(String executionId) {
        ScriptExecution execution = runningExecutions.get(executionId);
        if (execution != null) {
            Log.d(TAG, "停止脚本执行: " + executionId);
            execution.getEngine().forceStop();
        } else {
            Log.w(TAG, "找不到要停止的脚本执行: " + executionId);
        }
    }
    
    public void stopAllExecutions() {
        Log.d(TAG, "停止所有脚本执行，数量: " + runningExecutions.size());
        for (ScriptExecution execution : runningExecutions.values()) {
            execution.getEngine().forceStop();
        }
        runningExecutions.clear();
    }
}