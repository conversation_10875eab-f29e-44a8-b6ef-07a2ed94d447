package com.stardust.autojs.execution;

/**
 * 脚本执行配置类
 */
public class ExecutionConfig {
    private long delay = 0;
    private boolean loggingEnabled = true;
    private boolean stopAllOnEnd = false;
    private long timeout = 0;
    private int priority = 0;
    
    /**
     * 获取执行前延迟时间（毫秒）
     */
    public long getDelay() {
        return delay;
    }

    /**
     * 设置执行前延迟时间（毫秒）
     */
    public void setDelay(long delay) {
        this.delay = delay;
    }

    /**
     * 是否启用日志记录
     */
    public boolean isLoggingEnabled() {
        return loggingEnabled;
    }

    /**
     * 设置是否启用日志记录
     */
    public void setLoggingEnabled(boolean loggingEnabled) {
        this.loggingEnabled = loggingEnabled;
    }

    /**
     * 脚本结束时是否停止所有其他脚本
     */
    public boolean isStopAllOnEnd() {
        return stopAllOnEnd;
    }

    /**
     * 设置脚本结束时是否停止所有其他脚本
     */
    public void setStopAllOnEnd(boolean stopAllOnEnd) {
        this.stopAllOnEnd = stopAllOnEnd;
    }
    
    /**
     * 获取执行超时时间（毫秒），0表示无超时
     */
    public long getTimeout() {
        return timeout;
    }
    
    /**
     * 设置执行超时时间（毫秒），0表示无超时
     */
    public void setTimeout(long timeout) {
        this.timeout = timeout;
    }
    
    /**
     * 获取执行优先级
     */
    public int getPriority() {
        return priority;
    }
    
    /**
     * 设置执行优先级
     */
    public void setPriority(int priority) {
        this.priority = priority;
    }
}