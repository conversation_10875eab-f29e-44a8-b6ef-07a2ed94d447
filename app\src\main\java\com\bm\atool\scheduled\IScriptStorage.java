package com.bm.atool.scheduled;

import com.bm.atool.model.ScriptTask;

import java.util.List;

/**
 * 脚本存储接口
 * 负责脚本文件和任务状态的持久化存储
 */
public interface IScriptStorage {
    
    // ========== 脚本存储相关 ==========
    
    /**
     * 保存脚本内容
     * @param scriptId 脚本ID
     * @param content 脚本内容
     * @return 是否保存成功
     */
    boolean saveScript(String scriptId, String content);
    
    /**
     * 加载脚本内容
     * @param scriptId 脚本ID
     * @return 脚本内容，如果不存在返回null
     */
    String loadScript(String scriptId);
    
    /**
     * 删除脚本
     * @param scriptId 脚本ID
     * @return 是否删除成功
     */
    boolean deleteScript(String scriptId);
    
    /**
     * 检查脚本是否存在
     * @param scriptId 脚本ID
     * @return 是否存在
     */
    boolean scriptExists(String scriptId);
    
    /**
     * 保存脚本元数据
     * @param scriptId 脚本ID
     * @param metadata 元数据（JSON格式）
     * @return 是否保存成功
     */
    boolean saveScriptMetadata(String scriptId, String metadata);
    
    /**
     * 加载脚本元数据
     * @param scriptId 脚本ID
     * @return 元数据，如果不存在返回null
     */
    String loadScriptMetadata(String scriptId);
    
    /**
     * 获取所有脚本ID列表
     * @return 脚本ID列表
     */
    List<String> getAllScriptIds();
    
    // ========== 任务持久化相关 ==========
    
    /**
     * 保存任务状态
     * @param task 任务对象
     * @return 是否保存成功
     */
    boolean saveTask(ScriptTask task);
    
    /**
     * 加载任务状态
     * @param taskId 任务ID
     * @return 任务对象，如果不存在返回null
     */
    ScriptTask loadTask(String taskId);
    
    /**
     * 加载所有任务
     * @return 任务列表
     */
    List<ScriptTask> loadAllTasks();
    
    /**
     * 删除任务
     * @param taskId 任务ID
     * @return 是否删除成功
     */
    boolean deleteTask(String taskId);
    
    /**
     * 加载持久化任务
     * 只加载标记为persistent的任务
     * @return 持久化任务列表
     */
    List<ScriptTask> loadPersistentTasks();
    
    // ========== 执行历史相关 ==========
    
    /**
     * 保存执行历史记录
     * @param taskId 任务ID
     * @param executionId 执行ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param success 是否成功
     * @param result 执行结果
     * @param error 错误信息
     * @return 是否保存成功
     */
    boolean saveExecutionHistory(String taskId, String executionId, long startTime, 
                                long endTime, boolean success, String result, String error);
    
    /**
     * 获取任务的执行历史
     * @param taskId 任务ID
     * @param limit 限制返回数量，-1表示不限制
     * @return 执行历史列表（JSON格式）
     */
    List<String> getExecutionHistory(String taskId, int limit);
    
    /**
     * 清理执行历史
     * @param olderThanMs 清理多少毫秒前的历史记录
     * @return 清理的记录数量
     */
    int cleanupExecutionHistory(long olderThanMs);
    
    // ========== 存储管理相关 ==========
    
    /**
     * 获取存储使用情况
     * @return 存储使用情况（字节）
     */
    long getStorageUsage();
    
    /**
     * 清理临时文件和过期数据
     * @return 清理的文件数量
     */
    int cleanup();
    
    /**
     * 初始化存储
     * 创建必要的目录结构等
     * @return 是否初始化成功
     */
    boolean initialize();
    
    /**
     * 销毁存储
     * 清理资源
     */
    void destroy();
}