package com.stardust.autojs;

import android.content.Context;

/**
 * AutoJs主类，提供全局单例和核心服务访问
 */
public class AutoJs {
    private static AutoJs instance;
    private final Context context;
    private final ScriptEngineService scriptEngineService;

    private AutoJs(Context context) {
        this.context = context.getApplicationContext();
        this.scriptEngineService = new ScriptEngineService();
    }

    public static synchronized AutoJs getInstance() {
        return instance;
    }

    public static synchronized AutoJs initInstance(Context context) {
        if (instance == null) {
            instance = new AutoJs(context);
        }
        return instance;
    }

    public ScriptEngineService getScriptEngineService() {
        return scriptEngineService;
    }

    public Context getContext() {
        return context;
    }
}